# Node.js dependencies
node_modules/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Generated reports
reports/

# Cache files
cache/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
.npm
.eslintcache

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# dotenv environment variable files
.env.local
.env.development.local
.env.test.local
.env.production.local
