{"name": "qualifuse-mqtt-monitor", "version": "1.0.0", "description": "MQTT monitoring service for Qualifuse testing", "main": "mqtt-monitor.js", "scripts": {"start": "node mqtt-monitor.js", "dev": "nodemon mqtt-monitor.js", "dev:stable": "nodemon --delay 5000 --ignore cache/ --ignore reports/ mqtt-monitor.js", "prod": "NODE_ENV=production node mqtt-monitor.js", "test": "node test-mqtt.js", "cache:clear": "node dev-helper.js cache:clear", "cache:view": "node dev-helper.js cache:view", "cache:info": "node dev-helper.js cache:info", "status": "node dev-helper.js status", "reports:view": "node dev-helper.js reports:view", "reports:today": "node dev-helper.js reports:today"}, "dependencies": {"mqtt": "^5.3.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}