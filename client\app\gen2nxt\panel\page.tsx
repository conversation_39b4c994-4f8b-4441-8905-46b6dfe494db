'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle } from 'lucide-react';
import PCBDashboard from '@/components/pcb-gen2nxt';
import { formatTime, hasDeviceFailed, getBatteryIndicator } from '@/lib/dashboard-utils';
import { updateProgressBars, setupProgressBarObserver } from '@/lib/progress-bar';
import { API_CONFIG, formatDateForApi, apiRequest } from '@/lib/api-config';
import { QuickPCBLoader } from '@/components/ui/electronics-loader';

// Import types, hooks, and utilities
import { TestReport, DeviceData, TestStats } from '@/lib/types';
import { TEST_STATUS } from '@/lib/constants';
import { useSSEDeviceData } from '@/hooks/use-sse-device-data';
import { productVariants } from '@/lib/product-data';

import { statsToReport } from '@/lib/test-utils';
// Import custom components
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { ControlPanel } from '@/components/dashboard/control-panel';
import { DeviceCard } from '@/components/dashboard/device-card';

// Helper function to determine if a history device has failed
const hasHistoryDeviceFailed = (device: DeviceData): boolean => {
  // Check if any of the required status values are false
  if (typeof device.chargingStatus === 'boolean' && device.chargingStatus === false) {
    return true;
  }
  if (typeof device.dischargingStatus === 'boolean' && device.dischargingStatus === false) {
    return true;
  }
  if (
    typeof device.chargingAndDischargingStatus === 'boolean' &&
    device.chargingAndDischargingStatus === false &&
    device.hasState3Occurred
  ) {
    return true;
  }

  // If all required status values are true, the device has passed
  return false;
};

// Helper function to generate cache key
const generateCacheKey = (date: Date, serialNumber: string): string => {
  return `${formatDateForApi(date)}_${serialNumber}`;
};

// Helper function to check if cache is valid (within 5 minutes)
const isCacheValid = (timestamp: number): boolean => {
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
  return Date.now() - timestamp < CACHE_DURATION;
};

const fetchTestStatsForDate = async (date: Date): Promise<Partial<TestStats>> => {
  try {
    const formattedDate = formatDateForApi(date);
    const url = API_CONFIG.ENDPOINTS.TEST_STATS(formattedDate);
    const result = await apiRequest<TestStats>(url);

    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch test stats');
    }

    return {
      ...result.data,
      duration: result.data?.duration || 0, // Ensure duration is set to 0 if not provided
    };
  } catch (error) {
    console.error('Error fetching test stats:', error);
    return {
      duration: 0,
      panelsTested: 0,
      pcbsTested: 0,
      successPcbs: 0,
      failurePcbs: 0,
    };
  }
};

export default function PanelDashboard() {
  // State to control PCB dashboard visibility
  const [showPCBDashboard, setShowPCBDashboard] = useState(false);
  const [selectedPCB, setSelectedPCB] = useState<string | null>(null);

  // View options state
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');

  // History view state
  const [historyMode, setHistoryMode] = useState<boolean>(false);
  const [historyDevices, setHistoryDevices] = useState<DeviceData[]>([]);
  const [isLoadingHistoryData, setIsLoadingHistoryData] = useState<boolean>(false);
  const [historyDate, setHistoryDate] = useState<Date | null>(new Date());

  // Product variant selection state
  const [selectedVariant, setSelectedVariant] = useState<string>('Standard');

  // Serial number input state
  const [serialNumber, setSerialNumber] = useState<string>('');

  // Add this state for history test stats
  const [historyTestStats, setHistoryTestStats] = useState<TestStats>({
    duration: 0,
    panelsTested: 0,
    pcbsTested: 0,
    successPcbs: 0,
    failurePcbs: 0,
  });

  // Cache for history data to avoid unnecessary re-fetching
  const [historyDataCache, setHistoryDataCache] = useState<{
    [key: string]: {
      devices: DeviceData[];
      testStats: TestStats;
      timestamp: number;
    };
  }>({});

  // Track the current cache key to avoid duplicate requests
  const [currentCacheKey, setCurrentCacheKey] = useState<string>('');

  // Use custom hook for device data and test statistics
  const { devices, testResults, testStats, serialNumber: deviceSerialNumber } = useSSEDeviceData();

  // Update serial number from SSE when available
  useEffect(() => {
    if (deviceSerialNumber && !serialNumber) {
      setSerialNumber(deviceSerialNumber);
    }
  }, [deviceSerialNumber, serialNumber]);

  // Setup progress bar observer
  useEffect(() => {
    // Update progress bars when devices change
    updateProgressBars();

    // Setup observer for future updates
    const cleanup = setupProgressBarObserver();

    // Cleanup on unmount
    return cleanup;
  }, [devices, historyDevices]);

  // Generate sample reports from test stats
  const availableReports: TestReport[] = useMemo(() => {
    // Create a sample report from current test stats
    const currentReport = statsToReport(testStats, serialNumber, selectedVariant);

    // Add a sample historical report
    const historicalReport: TestReport = {
      id: 'rep-001',
      date: '2025-04-15',
      variant: 'Standard',
      serialNumber: 'SN-12345-A',
      successPcbs: 9,
      failurePcbs: 0,
    };

    return [currentReport, historicalReport];
  }, [testStats, serialNumber, selectedVariant]);

  // Handle report download
  const handleDownloadReport = () => {
    // This function is now handled in the control panel component
    console.log('Download report triggered from panel page');
  };

  // Handle history serial number selection
  const handleHistorySerialNumberSelect = async (serialNumber: string, date?: Date) => {
    if (!serialNumber) return;

    // Get the selected date (use provided date or current history date)
    const selectedDate = date || historyDate || new Date();

    // Generate cache key for this request
    const cacheKey = generateCacheKey(selectedDate, serialNumber);

    // Check if this is the same request as currently loaded
    if (currentCacheKey === cacheKey) {
      console.log('Same data already loaded, skipping fetch');
      return;
    }

    // Check if data is already cached and valid
    const cachedData = historyDataCache[cacheKey];
    if (cachedData && isCacheValid(cachedData.timestamp)) {
      console.log('Using cached data for:', cacheKey);
      setHistoryDevices(cachedData.devices);
      setHistoryTestStats(cachedData.testStats);
      setCurrentCacheKey(cacheKey);

      // Update the history date if provided
      if (date) {
        setHistoryDate(date);
      }
      return;
    }

    // Update the history date if provided
    if (date) {
      setHistoryDate(date);
      // Fetch test stats for the selected date
      const stats = await fetchTestStatsForDate(date);
      setHistoryTestStats(stats as TestStats);
    }

    setIsLoadingHistoryData(true);

    try {
      const formattedDate = formatDateForApi(selectedDate);

      // Fetch the report data for the selected serial number
      const url = API_CONFIG.ENDPOINTS.REPORTS.BY_DATE_AND_SERIAL(formattedDate, serialNumber);
      const result = await apiRequest(url);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch report data');
      }

      const data = result.data;
      console.log('History data fetched for:', cacheKey, data);

      // Convert the report data to DeviceData format
      if (data.data && Array.isArray(data.data)) {
        const historyDeviceData: DeviceData[] = data.data.map((item: any, index: number) => {
          // Ensure unique device ID by using index + 1 as fallback and primary source
          const deviceId = item['ID'] ? parseInt(item['ID']) : index + 1;
          // If parsing fails or results in invalid ID, use index + 1
          const finalDeviceId = isNaN(deviceId) || deviceId <= 0 ? index + 1 : deviceId;

          // Convert string 'true'/'false' to boolean
          const chargingStatus = item['ChargingStatus'] === 'true';
          const dischargingStatus = item['DischargingStatus'] === 'true';

          // Check if ChargingDischargingStatus exists and is not 'NA'
          const hasState3 = item['ChargingDischargingStatus'] !== undefined;
          const chargingAndDischargingStatus = hasState3
            ? item['ChargingDischargingStatus'] === 'true'
            : false;

          // Create a device data object
          return {
            id: finalDeviceId,
            name: finalDeviceId.toString(),
            isCharging: false, // Not active in history view
            isDischarging: false, // Not active in history view
            isChargingAndDischarging: false, // Not active in history view
            hasState3Occurred: hasState3,
            batteryLevel: parseFloat(item['bp'] || '0'),
            progress: 100, // Completed in history view
            timeRemaining: 0, // Completed in history view
            chargingVoltage: parseFloat(item['ChargingVoltage'] || '0'),
            chargingCurrent: parseFloat(item['ChargingCurrent'] || '0'),
            dischargingVoltage: parseFloat(item['DischargingVoltage'] || '0'),
            dischargingCurrent: parseFloat(item['DischargingCurrent'] || '0'),
            chargingStatus: chargingStatus,
            dischargingStatus: dischargingStatus,
            chargingAndDischargingStatus: chargingAndDischargingStatus,
            chargingStatusMessage: chargingStatus ? 'Passed' : 'Failed',
            dischargingStatusMessage: dischargingStatus ? 'Passed' : 'Failed',
            chargingAndDischargingStatusMessage: hasState3
              ? chargingAndDischargingStatus
                ? 'Passed'
                : 'Failed'
              : 'Not Tested',
            // For history view, all tests are completed, so we only need to determine if they passed or failed
            testStatus: TEST_STATUS.COMPLETED,
          };
        });

        // Get current test stats (either from previous fetch or fetch new ones)
        let currentTestStats = historyTestStats;
        if (date) {
          const stats = await fetchTestStatsForDate(date);
          currentTestStats = stats as TestStats;
          setHistoryTestStats(currentTestStats);
        }

        // Cache the data
        setHistoryDataCache(prev => ({
          ...prev,
          [cacheKey]: {
            devices: historyDeviceData,
            testStats: currentTestStats,
            timestamp: Date.now(),
          },
        }));

        // Update current state
        setHistoryDevices(historyDeviceData);
        setCurrentCacheKey(cacheKey);
      }
    } catch (error) {
      console.error('Error fetching history data:', error);
      setHistoryDevices([]);
    } finally {
      setIsLoadingHistoryData(false);
    }
  };

  // Handle PCB selection
  const handlePCBSelect = (pcbId: string) => {
    setSelectedPCB(pcbId);
    setShowPCBDashboard(true);
  };

  // Add a function to handle history date selection
  const handleHistoryDateSelect = async (date?: Date) => {
    if (date) {
      setHistoryDate(date);

      // Clear current cache key when date changes to force fresh data
      setCurrentCacheKey('');

      // Fetch test stats for the selected date
      const stats = await fetchTestStatsForDate(date);
      setHistoryTestStats(stats as TestStats);

      // No need to call fetchSerialNumbersForDate here as it's handled in the ControlPanel
    }
  };

  // Function to clear cache when switching between live and history modes
  const clearHistoryCache = () => {
    setHistoryDataCache({});
    setCurrentCacheKey('');
    setHistoryDevices([]);
  };

  // No longer needed since we removed the actions column

  // Group devices by channel
  const devicesByChannel = useMemo(() => {
    const channelMap: { [key: number]: DeviceData[] } = {};

    devices.forEach((device: DeviceData) => {
      // Calculate channel based on device ID (assuming 3 devices per channel)
      // This is a simplified approach - in a real app, you'd get this from your data
      const channelIndex = Math.floor((device.id - 1) / 3);

      if (!channelMap[channelIndex]) {
        channelMap[channelIndex] = [];
      }

      channelMap[channelIndex].push(device);
    });

    return channelMap;
  }, [devices]);

  // If showing PCB dashboard, render it
  if (showPCBDashboard && selectedPCB) {
    return (
      <div className="p-4 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg page-enter">
        <div className="mb-4 dashboard-header-enter">
          <Button
            variant="outline"
            onClick={() => {
              setShowPCBDashboard(false);
              setSelectedPCB(null);
            }}
            className="transition-all duration-200 hover:scale-105"
          >
            Back to Panel View
          </Button>
          <h2 className="text-2xl font-bold mt-2 animate-fade-in">PCB {selectedPCB} Details</h2>
        </div>
        <div className="pcb-grid-enter">
          <PCBDashboard
            device={devices.find((d: DeviceData) => d.id.toString() === selectedPCB) || null}
          />
        </div>
      </div>
    );
  }

  // Get the current product variant details
  const currentVariant = productVariants[selectedVariant as keyof typeof productVariants];

  return (
    <div className="p-4 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg page-enter">
      {/* Dashboard heading and stats - use historyTestStats when in history mode */}
      <DashboardHeader
        title={`${currentVariant.name} Testing Dashboard`}
        description={
          historyMode
            ? `Historical Data - ${historyDate?.toLocaleDateString()}`
            : currentVariant.description
        }
        testStats={historyMode ? historyTestStats : testStats}
        className="dashboard-header-enter"
      />

      {/* Control panel with view toggle and history mode */}
      <div className="control-panel-enter">
        <ControlPanel
          selectedVariant={selectedVariant}
          setSelectedVariant={setSelectedVariant}
          serialNumber={serialNumber}
          setSerialNumber={setSerialNumber}
          productVariants={productVariants}
          availableReports={availableReports}
          onDownloadReport={handleDownloadReport}
          viewMode={viewMode}
          setViewMode={setViewMode}
          historyMode={historyMode}
          setHistoryMode={setHistoryMode}
          onHistorySerialNumberSelect={handleHistorySerialNumberSelect}
          onHistoryDateSelect={handleHistoryDateSelect}
          onClearHistoryCache={clearHistoryCache}
        />
      </div>

      <div className="pcb-grid-enter">
        {historyMode ? (
          // History view - always use list view with smooth transitions
          <div className="border rounded-lg overflow-hidden transition-all duration-300 ease-in-out">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 transition-colors duration-200">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider transition-colors duration-200">
                    Device
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider transition-colors duration-200">
                    Battery
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider transition-colors duration-200">
                    Charging
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider transition-colors duration-200">
                    Discharging
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider transition-colors duration-200">
                    Charging+Discharging
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider transition-colors duration-200">
                    Result
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoadingHistoryData ? (
                  <tr>
                    <td colSpan={8} className="px-3 py-4 text-center">
                      <div className="flex justify-center items-center loading-fade-in">
                        <QuickPCBLoader
                          scenario="dataProcessing"
                          size="md"
                          customMessage="Loading PCB test history..."
                        />
                      </div>
                    </td>
                  </tr>
                ) : historyDevices.length > 0 ? (
                  historyDevices.map((device, index) => (
                    <tr
                      key={`history-device-${device.id}-${index}`}
                      className={`${
                        hasHistoryDeviceFailed(device)
                          ? 'bg-red-50 border-l-2 border-red-300 shadow-md shadow-red-200 relative pcb-status-error'
                          : 'bg-green-50 border-l-2 border-green-300 shadow-md shadow-green-200 relative pcb-status-success'
                      } transform transition-all duration-300 ease-out hover:scale-[1.01] hover:shadow-lg animate-fade-in-up pcb-list-row`}
                      style={
                        {
                          '--animation-delay': `${index * 50}ms`,
                          animationDelay: `${index * 50}ms`,
                          animationFillMode: 'both',
                        } as React.CSSProperties
                      }
                    >
                      <td className="px-3 py-2 whitespace-nowrap relative">
                        <div className="text-sm font-medium text-gray-900">Device {device.id}</div>
                        <div className="text-xs text-gray-500">
                          {formatTime(device.timeRemaining)}
                        </div>
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap">
                        <div className="flex items-center">
                          {getBatteryIndicator(
                            device.batteryLevel,
                            device.isCharging,
                            device.isChargingAndDischarging,
                            'sm'
                          )}
                          <span className="text-sm ml-1">{device.batteryLevel}%</span>
                        </div>
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap">
                        <div className="flex items-center gap-1">
                          {device.chargingStatus === true ? (
                            <CheckCircle className="h-4 w-4 text-green-500 mr-1 icon-transition status-indicator success" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-500 mr-1 icon-transition status-indicator error" />
                          )}
                          <span
                            className={`text-sm font-medium ${
                              device.chargingStatus === true ? 'text-green-600' : 'text-red-600'
                            }`}
                          >
                            {device.chargingStatus === true ? 'Passed' : 'Failed'}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 mt-1">
                          <span className="text-xs text-muted-foreground">V:</span>
                          <span className="text-xs font-medium">
                            {(device.chargingVoltage || 0).toFixed(2)}V
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-xs text-muted-foreground">A:</span>
                          <span className="text-xs font-medium">
                            {(device.chargingCurrent || 0).toFixed(3)}A
                          </span>
                        </div>
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap">
                        <div className="flex items-center gap-1">
                          {device.dischargingStatus === true ? (
                            <CheckCircle className="h-4 w-4 text-green-500 mr-1 icon-transition status-indicator success" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-500 mr-1 icon-transition status-indicator error" />
                          )}
                          <span
                            className={`text-sm font-medium ${
                              device.dischargingStatus === true ? 'text-green-600' : 'text-red-600'
                            }`}
                          >
                            {device.dischargingStatus === true ? 'Passed' : 'Failed'}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 mt-1">
                          <span className="text-xs text-muted-foreground">V:</span>
                          <span className="text-xs font-medium">
                            {(device.dischargingVoltage || 0).toFixed(2)}V
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-xs text-muted-foreground">A:</span>
                          <span className="text-xs font-medium">
                            {(device.dischargingCurrent || 0).toFixed(3)}A
                          </span>
                        </div>
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap">
                        {device.hasState3Occurred ? (
                          <div className="flex items-center gap-1">
                            {device.chargingAndDischargingStatus === true ? (
                              <CheckCircle className="h-4 w-4 text-green-500 mr-1 icon-transition status-indicator success" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500 mr-1 icon-transition status-indicator error" />
                            )}
                            <span
                              className={`text-sm font-medium ${
                                device.chargingAndDischargingStatus === true
                                  ? 'text-green-600'
                                  : 'text-red-600'
                              }`}
                            >
                              {device.chargingAndDischargingStatus === true ? 'Passed' : 'Failed'}
                            </span>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-400">Not Active</div>
                        )}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap">
                        <div
                          className={`text-sm font-medium ${
                            hasHistoryDeviceFailed(device) ? 'text-red-600' : 'text-green-600'
                          }`}
                        >
                          {hasHistoryDeviceFailed(device) ? 'Failed' : 'Passed'}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="px-3 py-4 text-center">
                      <p className="text-slate-600">
                        No history data available for the selected serial number.
                      </p>
                      <p className="text-slate-500 text-sm mt-1">
                        Please select a different serial number or date.
                      </p>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        ) : (
          // Card view for live data - organized by channels with smooth transitions
          <div className="space-y-4 transition-all duration-300 ease-in-out">
            {Object.entries(devicesByChannel).map(([channelIndex, channelDevices], channelIdx) => {
              // Calculate grid columns based on number of devices in this channel
              const deviceCount = channelDevices.length;
              let gridClass = '';

              // Determine grid class based on device count
              if (deviceCount === 1) {
                gridClass = 'grid-cols-1';
              } else if (deviceCount === 2) {
                gridClass = 'grid-cols-1 sm:grid-cols-2';
              } else if (deviceCount === 3) {
                gridClass = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3';
              } else {
                gridClass = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
              }

              return (
                <div
                  key={channelIndex}
                  className="border rounded-lg p-3 bg-white/80 transition-all duration-300 ease-out hover:shadow-lg hover:bg-white/90 animate-fade-in-up hover-lift"
                  style={
                    {
                      '--animation-delay': `${channelIdx * 100}ms`,
                      animationDelay: `${channelIdx * 100}ms`,
                      animationFillMode: 'both',
                    } as React.CSSProperties
                  }
                >
                  <div className={`grid ${gridClass} gap-3 w-full`}>
                    {channelDevices.map((device, deviceIdx) => (
                      <div
                        key={device.id}
                        className={`${
                          device.testStatus === 'completed'
                            ? hasDeviceFailed(device, testResults)
                              ? 'rounded-lg shadow-lg shadow-red-200 p-1 bg-red-50'
                              : 'rounded-lg shadow-lg shadow-green-200 p-1 bg-green-50'
                            : ''
                        } transform transition-all duration-300 ease-out hover:scale-105 animate-fade-in-up hover-scale`}
                        style={
                          {
                            '--animation-delay': `${channelIdx * 100 + deviceIdx * 50}ms`,
                            animationDelay: `${channelIdx * 100 + deviceIdx * 50}ms`,
                            animationFillMode: 'both',
                          } as React.CSSProperties
                        }
                      >
                        <DeviceCard
                          device={device}
                          onSelect={handlePCBSelect}
                          compact={true}
                          testResults={testResults}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
