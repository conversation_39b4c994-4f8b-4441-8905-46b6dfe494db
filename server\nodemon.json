{"watch": ["mqtt-monitor.js", ".env"], "ignore": ["node_modules/", "reports/", "cache/", "public/", "*.csv", "*.json", "*.log", "test-*.js"], "ext": "js,env", "delay": 2000, "verbose": true, "restartable": "rs", "env": {"NODE_ENV": "development"}, "events": {"start": "echo 'Starting MQTT Monitor with nodemon...'", "restart": "echo 'Restarting MQTT Monitor due to file changes...'", "crash": "echo 'MQTT Monitor crashed, waiting for file changes to restart...'"}}