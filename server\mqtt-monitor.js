/**
 * MQTT Monitoring Service for Qualifuse Testing
 *
 * This service connects to the MQTT broker, monitors test data,
 * and generates CSV reports when tests complete.
 */

const mqtt = require('mqtt');
const fs = require('fs');
const path = require('path');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const dotenv = require('dotenv');
const express = require('express');
const cors = require('cors');

// Store active SSE clients
const sseClients = new Set();

// Load environment variables
dotenv.config();

// MQTT connection configuration (same as client code)
const MQTT_CONFIG = {
  url: process.env.MQTT_URL || 'ws://192.168.0.144:9001/',
  options: {
    username: process.env.MQTT_USERNAME || 'resonate',
    password: process.env.MQTT_PASSWORD || 'Resonate@123',
  },
  topic: process.env.MQTT_TOPIC || 'pico/testing',
};

// Device states (same as client code)
const DEVICE_STATE = {
  CHARGING: 1,
  DISCHARGING: 2,
  CHARGING_AND_DISCHARGING: 3,
  COMPLETED: 4,
};

// Create reports directory if it doesn't exist
const reportsDir = path.join(__dirname, 'reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

// Create public directory if it doesn't exist
const publicDir = path.join(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Create cache directory if it doesn't exist
const cacheDir = process.env.CACHE_DIR || path.join(__dirname, 'cache');
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir, { recursive: true });
}

// Class to manage device data and test state
class DeviceMonitor {
  constructor() {
    this.devices = new Map(); // Map to store device data by deviceId
    this.currentState = 0;
    this.testInProgress = false;
    this.testCount = 0; // Counter for tests in the current day
    this.currentTestId = ''; // ID for the current test (kept for internal tracking)
    this.hasPCB = new Array(9).fill(false); // Array to track which devices have PCBs
    this.batteryLevels = new Array(9).fill(0); // Array to track battery levels

    // Current panel serial number (all devices in the same test/panel have the same serial number)
    this.currentPanelSerialNumber = '';

    // Track which states occurred during the test
    this.stateOccurred = {
      [DEVICE_STATE.CHARGING]: false,
      [DEVICE_STATE.DISCHARGING]: false,
      [DEVICE_STATE.CHARGING_AND_DISCHARGING]: false,
      [DEVICE_STATE.COMPLETED]: false,
    };

    // Duration tracking
    this.startTime = null; // When the first test of the day started
    this.lastTestEndTime = null; // When the last test ended
    this.currentDay = new Date().toISOString().split('T')[0]; // Current day in YYYY-MM-DD format

    // Test statistics
    this.testStats = {
      panelsTested: 0, // Number of unique serial numbers (panels)
      pcbsTested: 0, // Total number of devices tested
      successPcbs: 0, // Number of devices that passed all tests
      failurePcbs: 0, // Number of devices that failed any test
      duration: 0, // Duration in seconds
    };

    // Set to track unique panel serial numbers
    this.testedPanels = new Set();

    // Cache file path for current day's test stats
    this.cacheFilePath = path.join(cacheDir, `teststats_${this.currentDay}.json`);

    // Load cached data on startup
    this.loadCachedTestStats();
  }

  // Load cached test stats from disk
  loadCachedTestStats() {
    try {
      if (fs.existsSync(this.cacheFilePath)) {
        const cachedData = JSON.parse(fs.readFileSync(this.cacheFilePath, 'utf8'));

        // Validate that the cached data is for today
        if (cachedData.date === this.currentDay) {
          console.log('Loading cached test stats from:', this.cacheFilePath);

          // Check if cache is recent (within last 10 minutes) to avoid loading stale data during development
          const lastUpdated = new Date(cachedData.lastUpdated);
          const now = new Date();
          const timeDiff = now - lastUpdated;
          const isRecent = timeDiff < 10 * 60 * 1000; // 10 minutes

          if (process.env.NODE_ENV === 'development' && !isRecent) {
            console.log('Cache is older than 10 minutes in development mode, starting fresh');
            return;
          }

          // Restore test stats
          this.testStats = { ...this.testStats, ...cachedData.testStats };

          // Restore timing data
          if (cachedData.startTime) {
            this.startTime = new Date(cachedData.startTime);
          }
          if (cachedData.lastTestEndTime) {
            this.lastTestEndTime = new Date(cachedData.lastTestEndTime);
          }

          // Restore test count and tested panels
          this.testCount = cachedData.testCount || 0;
          this.testedPanels = new Set(cachedData.testedPanels || []);

          // Update duration based on current time
          this.updateDuration();

          console.log('Restored test stats:', this.testStats);
          console.log('Restored tested panels:', Array.from(this.testedPanels));
          console.log('Restored test count:', this.testCount);
          console.log(`Cache age: ${Math.round(timeDiff / 1000)} seconds`);
        } else {
          console.log('Cached data is for a different date, starting fresh');
          this.clearOldCache();
        }
      } else {
        console.log('No cache file found, starting with fresh test stats');
      }
    } catch (error) {
      console.error('Error loading cached test stats:', error);
      console.log('Starting with fresh test stats');
    }
  }

  // Save current test stats to cache
  saveCachedTestStats() {
    try {
      const cacheData = {
        date: this.currentDay,
        testStats: this.testStats,
        startTime: this.startTime ? this.startTime.toISOString() : null,
        lastTestEndTime: this.lastTestEndTime ? this.lastTestEndTime.toISOString() : null,
        testCount: this.testCount,
        testedPanels: Array.from(this.testedPanels),
        lastUpdated: new Date().toISOString(),
      };

      fs.writeFileSync(this.cacheFilePath, JSON.stringify(cacheData, null, 2));
      console.log('Test stats cached to:', this.cacheFilePath);
    } catch (error) {
      console.error('Error saving test stats to cache:', error);
    }
  }

  // Clear old cache files
  clearOldCache() {
    try {
      // Remove cache files older than 7 days
      const files = fs.readdirSync(cacheDir);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      files.forEach(file => {
        if (file.startsWith('teststats_') && file.endsWith('.json')) {
          const filePath = path.join(cacheDir, file);
          const stats = fs.statSync(filePath);

          if (stats.mtime < sevenDaysAgo) {
            fs.unlinkSync(filePath);
            console.log('Removed old cache file:', file);
          }
        }
      });
    } catch (error) {
      console.error('Error clearing old cache files:', error);
    }
  }

  // Reset for a new test
  reset() {
    this.devices.clear();
    this.currentState = 0;
    this.testInProgress = false;
    this.hasPCB = new Array(9).fill(false);
    this.batteryLevels = new Array(9).fill(0);
    this.currentPanelSerialNumber = ''; // Reset serial number

    // Reset state tracking
    Object.keys(this.stateOccurred).forEach(key => {
      this.stateOccurred[key] = false;
    });

    console.log('Device monitor reset for new test');
  }

  // Calculate current duration in seconds
  calculateDuration() {
    if (!this.startTime) {
      return 0;
    }

    // If no test is in progress, use the last test end time
    const endTime = this.testInProgress ? new Date() : this.lastTestEndTime || new Date();
    return Math.floor((endTime - this.startTime) / 1000);
  }

  // Update duration in test stats
  updateDuration() {
    this.testStats.duration = this.calculateDuration();
    return this.testStats.duration;
  }

  // Check if day has changed and reset duration if needed
  checkDayChange() {
    const today = new Date().toISOString().split('T')[0];
    if (today !== this.currentDay) {
      console.log(`Day changed from ${this.currentDay} to ${today}, resetting duration tracking`);

      // Save current day's data before switching
      this.saveCachedTestStats();

      // Update to new day
      this.currentDay = today;
      this.cacheFilePath = path.join(cacheDir, `teststats_${this.currentDay}.json`);

      // Reset all stats for new day
      this.startTime = null;
      this.lastTestEndTime = null;
      this.testStats.duration = 0;
      this.testCount = 0;
      this.testedPanels.clear();
      this.testStats.panelsTested = 0;
      this.testStats.pcbsTested = 0;
      this.testStats.successPcbs = 0;
      this.testStats.failurePcbs = 0;

      // Load cache for new day (if any)
      this.loadCachedTestStats();

      // Clean up old cache files
      this.clearOldCache();
    }
  }

  // Prepare device data as arrays for SSE
  prepareDeviceArrays() {
    // Initialize arrays for each property
    const deviceCount = 9; // Total number of possible devices
    const serialNumber = this.currentPanelSerialNumber;
    const progress = new Array(deviceCount).fill(0);

    // Initialize result object with common properties
    const result = {
      serialNumber,
      progress,
    };

    // Only include state-specific properties based on which states have occurred
    if (this.stateOccurred[DEVICE_STATE.CHARGING] || this.currentState === DEVICE_STATE.CHARGING) {
      // Include charging-related properties
      const chargingStatus = new Array(deviceCount).fill(false);
      const chargingVoltage = new Array(deviceCount).fill(0);
      const chargingCurrent = new Array(deviceCount).fill(0);

      // Fill arrays with actual device data
      this.devices.forEach(device => {
        const index = device.deviceID - 1; // Convert to 0-based index

        // Only update if the device has a PCB
        if (this.hasPCB[index]) {
          chargingStatus[index] = device.chargingStatus === true;
          chargingVoltage[index] = device.chargingVoltage || 0;
          chargingCurrent[index] = device.chargingCurrent || 0;
          progress[index] = device.progress || 0;
        }
      });

      // Add to result
      result.chargingStatus = chargingStatus;
      result.chargingVoltage = chargingVoltage;
      result.chargingCurrent = chargingCurrent;
    }

    if (
      this.stateOccurred[DEVICE_STATE.DISCHARGING] ||
      this.currentState === DEVICE_STATE.DISCHARGING
    ) {
      // Include discharging-related properties
      const dischargingStatus = new Array(deviceCount).fill(false);
      const dischargingVoltage = new Array(deviceCount).fill(0);
      const dischargingCurrent = new Array(deviceCount).fill(0);

      // Fill arrays with actual device data
      this.devices.forEach(device => {
        const index = device.deviceID - 1; // Convert to 0-based index

        // Only update if the device has a PCB
        if (this.hasPCB[index]) {
          dischargingStatus[index] = device.dischargingStatus === true;
          dischargingVoltage[index] = device.dischargingVoltage || 0;
          dischargingCurrent[index] = device.dischargingCurrent || 0;
          progress[index] = device.progress || 0;
        }
      });

      // Add to result
      result.dischargingStatus = dischargingStatus;
      result.dischargingVoltage = dischargingVoltage;
      result.dischargingCurrent = dischargingCurrent;
    }

    if (
      this.stateOccurred[DEVICE_STATE.CHARGING_AND_DISCHARGING] ||
      this.currentState === DEVICE_STATE.CHARGING_AND_DISCHARGING
    ) {
      // Include charging-and-discharging-related properties
      const chargingAndDischargingStatus = new Array(deviceCount).fill(false);

      // Fill arrays with actual device data
      this.devices.forEach(device => {
        const index = device.deviceID - 1; // Convert to 0-based index

        // Only update if the device has a PCB
        if (this.hasPCB[index]) {
          chargingAndDischargingStatus[index] = device.chargingAndDischargingStatus === true;
          progress[index] = device.progress || 0;
        }
      });

      // Add to result
      result.chargingAndDischargingStatus = chargingAndDischargingStatus;
    }

    return result;
  }

  // Send current test data to SSE clients
  sendTestDataToSSEClients() {
    try {
      if (sseClients.size === 0) return; // No clients connected

      // Send device data only when test is running
      // PCB status is only sent when test starts, not with every update
      // Don't send currentState - let clients determine state from channel data
      const testData = {
        devices: Array.from(this.devices.values()),
      };

      // Send to all clients
      sendSSEToAllClients(testData);
    } catch (error) {
      console.error('Error sending test data to SSE clients:', error);
    }
  }

  // Initialize or reset device data
  initializeDevice(deviceId) {
    // Check if this device has a PCB
    if (!this.hasPCB[deviceId - 1]) {
      return null; // Skip devices without PCBs
    }

    // Ensure battery level is set correctly
    const batteryLevel = this.batteryLevels[deviceId - 1] || 0;
    console.log(`Initializing device ${deviceId} with battery level: ${batteryLevel}%`);

    return {
      deviceID: deviceId,
      // All devices in the same panel/test have the same serial number
      serialNumber: this.currentPanelSerialNumber,
      // Initialize all status values to false - they will be updated based on channel data
      chargingStatus: false,
      dischargingStatus: false,
      chargingAndDischargingStatus: false,
      chargingVoltage: 0,
      chargingCurrent: 0,
      dischargingVoltage: 0,
      dischargingCurrent: 0,
      batteryLevel: batteryLevel, // Use the battery level from the array
      testCompleted: false,
      // Keep timestamp for internal tracking but it won't be included in the CSV
      timestamp: new Date().toISOString(),
    };
  }

  // Process PCB status message
  processPCBStatusMessage(message) {
    console.log('PCB Status Message:', message);

    // Check if day has changed
    this.checkDayChange();

    // Handle test initialization if needed
    this.handleTestInitialization();

    // Update device data from message
    this.updateDeviceDataFromMessage(message);

    // Initialize devices with PCBs
    this.initializeDevicesWithPCBs();

    // Send SSE updates if needed
    this.sendSSEUpdatesForPCBStatus();
  }

  // Handle test initialization
  handleTestInitialization() {
    if (!this.testInProgress) {
      // If this is the first test of the day, start duration tracking
      if (!this.startTime) {
        this.startTime = new Date();
        console.log(`Starting duration tracking at ${this.startTime.toISOString()}`);
      }

      this.testCount++;
      const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      this.currentTestId = `${date}_Test${this.testCount}`;

      // If no serial number was received from MQTT, use 'NOSERIAL' as fallback
      if (!this.currentPanelSerialNumber) {
        this.currentPanelSerialNumber = 'NOSERIAL';
        console.log(
          'No serial number received from MQTT, using fallback:',
          this.currentPanelSerialNumber
        );
      }

      // Add this panel to the set of tested panels
      this.testedPanels.add(this.currentPanelSerialNumber);

      // Update the panelsTested count
      this.testStats.panelsTested = this.testedPanels.size;

      // Update duration
      this.updateDuration();

      console.log(
        `Starting new test: ${this.currentTestId} with panel serial number: ${this.currentPanelSerialNumber}`
      );
      console.log(`Updated test stats: ${JSON.stringify(this.testStats)}`);

      // Save updated stats to cache
      this.saveCachedTestStats();
    }
  }

  // Update device data from message
  updateDeviceDataFromMessage(message) {
    // Update PCB status (which devices have PCBs)
    if (Array.isArray(message.pcb_status) && message.pcb_status.length > 0) {
      this.hasPCB = message.pcb_status.map(status => Boolean(status));
      console.log('PCB Status updated:', this.hasPCB);

      // Reset serial number when a new PCB status message is received
      // This ensures we don't reuse the previous test's serial number
      if (!message.sn && !this.testInProgress) {
        this.currentPanelSerialNumber = '';
      }
    }

    // Update battery levels
    if (Array.isArray(message.bp) && message.bp.length > 0) {
      this.batteryLevels = message.bp.map(level => Number(level));
      console.log('Battery Levels updated:', this.batteryLevels);
    }

    // Update serial number if provided in the message
    if (message.sn) {
      this.currentPanelSerialNumber = message.sn;
      console.log('Serial Number received from MQTT:', this.currentPanelSerialNumber);
    }
  }

  // Initialize devices with PCBs
  initializeDevicesWithPCBs() {
    this.hasPCB.forEach((hasPCB, index) => {
      const deviceId = index + 1;
      if (hasPCB && !this.devices.has(deviceId)) {
        const device = this.initializeDevice(deviceId);
        if (device) {
          this.devices.set(deviceId, device);
        }
      }
    });
  }

  // Send SSE updates for PCB status
  sendSSEUpdatesForPCBStatus() {
    const wasTestInProgress = this.testInProgress;
    this.testInProgress = true;

    if (!wasTestInProgress && sseClients.size > 0) {
      // Prepare device data in array format
      const deviceArrays = this.prepareDeviceArrays();

      // Update duration before sending
      this.updateDuration();

      // Create the base message with common properties
      // Include PCB status and battery levels when test starts
      // This is the only time battery levels are sent during a test
      const message = {
        testStats: this.testStats,
        pcb_status: this.hasPCB, // Only sent when test is running
        bp: this.batteryLevels, // Include battery levels with PCB status
        serialNumber: this.currentPanelSerialNumber,
      };

      // Add state-specific properties only if the state has occurred
      if (
        this.currentState === DEVICE_STATE.CHARGING ||
        this.stateOccurred[DEVICE_STATE.CHARGING]
      ) {
        if (deviceArrays.chargingStatus) message.chargingStatus = deviceArrays.chargingStatus;
        if (deviceArrays.chargingVoltage) message.chargingVoltage = deviceArrays.chargingVoltage;
        if (deviceArrays.chargingCurrent) message.chargingCurrent = deviceArrays.chargingCurrent;
      }

      if (
        this.currentState === DEVICE_STATE.DISCHARGING ||
        this.stateOccurred[DEVICE_STATE.DISCHARGING]
      ) {
        if (deviceArrays.dischargingStatus)
          message.dischargingStatus = deviceArrays.dischargingStatus;
        if (deviceArrays.dischargingVoltage)
          message.dischargingVoltage = deviceArrays.dischargingVoltage;
        if (deviceArrays.dischargingCurrent)
          message.dischargingCurrent = deviceArrays.dischargingCurrent;
      }

      if (
        this.currentState === DEVICE_STATE.CHARGING_AND_DISCHARGING ||
        this.stateOccurred[DEVICE_STATE.CHARGING_AND_DISCHARGING]
      ) {
        if (deviceArrays.chargingAndDischargingStatus) {
          message.chargingAndDischargingStatus = deviceArrays.chargingAndDischargingStatus;
        }
      }

      // Always include progress
      if (deviceArrays.progress) message.progress = deviceArrays.progress;

      // Send the message
      sendSSEToAllClients(message);
    }
  }

  // Process state message
  processStateMessage(message) {
    console.log('State Message:', message);

    // Check if day has changed
    this.checkDayChange();

    const previousState = this.currentState;
    this.currentState = message.state;

    // Mark this state as occurred
    if (message.state in this.stateOccurred) {
      this.stateOccurred[message.state] = true;
    }

    // Handle test completion
    if (message.state === DEVICE_STATE.COMPLETED && previousState !== DEVICE_STATE.COMPLETED) {
      this.handleTestCompletion();
    }
    // Handle test start
    else if (message.state !== DEVICE_STATE.COMPLETED && !this.testInProgress) {
      this.handleTestStart(message.state);
    }

    // Log state information
    this.logStateInformation();

    // Send state update to SSE clients
    this.sendStateUpdateToSSE();
  }

  // Handle test completion
  handleTestCompletion() {
    console.log('Test completed (state 4) received. Generating report...');

    // Update the last test end time
    this.lastTestEndTime = new Date();
    console.log(`Test ended at ${this.lastTestEndTime.toISOString()}`);

    // Update duration
    this.updateDuration();
    console.log(`Current duration: ${this.testStats.duration} seconds`);

    this.generateReport();
    this.testInProgress = false;

    // Reset state tracking for next test
    this.resetStateTracking();

    // Save updated stats to cache
    this.saveCachedTestStats();

    // Send test completion message - no longer send testInProgress property
    // Clients determine state from message order
    sendSSEToAllClients({
      testStats: this.testStats,
    });
  }

  // Handle test start
  handleTestStart(state) {
    console.log(`Test starting with state ${state}`);

    // If this is the first test of the day, start duration tracking
    if (!this.startTime) {
      this.startTime = new Date();
      console.log(`Starting duration tracking at ${this.startTime.toISOString()}`);
    }

    this.testInProgress = true;

    // Reset device data for new test
    this.devices.clear();

    // Reset state tracking for new test
    this.resetStateTracking();

    // Mark current state as occurred
    this.stateOccurred[state] = true;

    // Update duration
    this.updateDuration();
  }

  // Reset state tracking
  resetStateTracking() {
    Object.keys(this.stateOccurred).forEach(key => {
      this.stateOccurred[key] = false;
    });
  }

  // Log state information
  logStateInformation() {
    // Log the current state for debugging
    console.log(
      `Current state: ${this.currentState} (${Object.keys(DEVICE_STATE).find(
        key => DEVICE_STATE[key] === this.currentState
      )})`
    );

    // Log which devices are being tracked
    console.log(
      `Currently tracking ${this.devices.size} devices for panel ${this.currentPanelSerialNumber}:`,
      Array.from(this.devices.keys())
        .map(id => `Device ${id}`)
        .join(', ')
    );

    console.log('States occurred in this test:', this.stateOccurred);
  }

  // Send state update to SSE clients
  sendStateUpdateToSSE() {
    try {
      if (sseClients.size > 0) {
        // Send only the state information for logging purposes
        // Don't send currentState for state determination - clients use channel data
        // Battery levels are only sent on initial connection and test start
        sendSSEToAllClients({
          state: this.currentState, // For logging/reference only
        });
      }
    } catch (error) {
      console.error('Error sending state update to SSE clients:', error);
    }
  }

  // Process channel data message
  processChannelDataMessage(channelsData) {
    try {
      console.log('Channel Data Message:', JSON.stringify(channelsData).substring(0, 200) + '...');

      let deviceGlobalIndex = 0;

      // Process each channel
      for (const channelData of channelsData) {
        try {
          // Skip invalid channel data
          if (!this.isValidChannelData(channelData)) {
            continue;
          }

          const channelIndex = channelData.channel;
          const deviceCount = channelData.progress.length;
          console.log(`Processing channel ${channelIndex} with ${deviceCount} devices`);

          // Process each device in the channel
          this.processDevicesInChannel(channelData, deviceCount, deviceGlobalIndex);
          deviceGlobalIndex += deviceCount;
        } catch (error) {
          console.error(
            `Error processing channel data for channel ${channelData?.channel}:`,
            error
          );
          // Continue processing other channels
        }
      }

      // Send channel data to SSE clients
      this.sendChannelDataToSSE(channelsData);
    } catch (error) {
      console.error('Error in processChannelDataMessage:', error);
    }
  }

  // Check if channel data is valid
  isValidChannelData(channelData) {
    if (
      !Array.isArray(channelData.progress) ||
      !Array.isArray(channelData.elapsed_time) ||
      channelData.progress.length === 0 ||
      channelData.elapsed_time.length === 0
    ) {
      console.warn(`Invalid channel data for channel ${channelData.channel}`);
      return false;
    }
    return true;
  }

  // Process devices in a channel
  processDevicesInChannel(channelData, deviceCount, startIndex) {
    for (let deviceIndexInChannel = 0; deviceIndexInChannel < deviceCount; deviceIndexInChannel++) {
      // Calculate the actual device index (global index)
      const deviceId = startIndex + deviceIndexInChannel + 1; // 1-based indexing like in the client

      // Skip devices without PCBs
      if (!this.hasPCB[deviceId - 1]) {
        continue;
      }

      // Get or initialize device
      const device = this.getOrInitializeDevice(deviceId);
      if (!device) {
        continue; // Skip if device is not available
      }

      // Update device with channel data
      this.updateDeviceWithChannelData(device, channelData, deviceIndexInChannel, deviceId);
    }
  }

  // Get or initialize a device
  getOrInitializeDevice(deviceId) {
    if (!this.devices.has(deviceId)) {
      const device = this.initializeDevice(deviceId);
      if (device) {
        this.devices.set(deviceId, device);
        return device;
      }
      return null;
    }
    return this.devices.get(deviceId);
  }

  // Update device with channel data
  updateDeviceWithChannelData(device, channelData, deviceIndex, deviceId) {
    // Create a copy of the device to update
    const updatedDevice = { ...device };

    // Update progress and time remaining
    updatedDevice.progress = channelData.progress[deviceIndex];
    updatedDevice.timeRemaining = Math.max(0, channelData.elapsed_time[deviceIndex]);

    // Check if status array is available
    const hasStatusData =
      Array.isArray(channelData.status) && channelData.status.length > deviceIndex;

    // Update charging values and status
    this.updateChargingValues(updatedDevice, channelData, deviceIndex, hasStatusData, deviceId);

    // Update discharging values and status
    this.updateDischargingValues(updatedDevice, channelData, deviceIndex, hasStatusData, deviceId);

    // Update charging and discharging status if in combined state
    this.updateCombinedStatus(updatedDevice, channelData, deviceIndex, hasStatusData, deviceId);

    // Update battery level from the PCB status message
    // Only update if the battery level is greater than 0
    if (this.batteryLevels[deviceId - 1] > 0) {
      updatedDevice.batteryLevel = this.batteryLevels[deviceId - 1];
      console.log(`Updated battery level for device ${deviceId}: ${updatedDevice.batteryLevel}%`);
    } else {
      // Preserve existing battery level if the new value is 0
      updatedDevice.batteryLevel = device.batteryLevel || 0;
      console.log(
        `Preserving existing battery level for device ${deviceId}: ${updatedDevice.batteryLevel}%`
      );
    }

    // Update timestamp
    updatedDevice.timestamp = new Date().toISOString();

    // Save updated device
    this.devices.set(deviceId, updatedDevice);
  }

  // Update charging values and status
  updateChargingValues(device, channelData, deviceIndex, hasStatusData, deviceId) {
    if (
      channelData.charging &&
      Array.isArray(channelData.charging.V) &&
      Array.isArray(channelData.charging.C)
    ) {
      device.chargingVoltage = channelData.charging.V[deviceIndex] || 0;
      device.chargingCurrent = channelData.charging.C[deviceIndex] || 0;

      // Update charging status based on the current state and status array
      if (this.currentState === DEVICE_STATE.CHARGING && hasStatusData) {
        device.chargingStatus = Boolean(channelData.status[deviceIndex]);
        console.log(`Device ${deviceId} charging status set to ${device.chargingStatus}`);
      }
    }
  }

  // Update discharging values and status
  updateDischargingValues(device, channelData, deviceIndex, hasStatusData, deviceId) {
    if (
      channelData.discharging &&
      Array.isArray(channelData.discharging.V) &&
      Array.isArray(channelData.discharging.C)
    ) {
      device.dischargingVoltage = channelData.discharging.V[deviceIndex] || 0;
      device.dischargingCurrent = channelData.discharging.C[deviceIndex] || 0;

      // Update discharging status based on the current state and status array
      if (this.currentState === DEVICE_STATE.DISCHARGING && hasStatusData) {
        device.dischargingStatus = Boolean(channelData.status[deviceIndex]);
        console.log(`Device ${deviceId} discharging status set to ${device.dischargingStatus}`);
      }
    }
  }

  // Update combined charging and discharging status
  updateCombinedStatus(device, channelData, deviceIndex, hasStatusData, deviceId) {
    if (this.currentState === DEVICE_STATE.CHARGING_AND_DISCHARGING && hasStatusData) {
      device.chargingAndDischargingStatus = Boolean(channelData.status[deviceIndex]);
      console.log(
        `Device ${deviceId} charging and discharging status set to ${device.chargingAndDischargingStatus}`
      );
    }
  }

  // Send channel data to SSE clients
  sendChannelDataToSSE(channelsData) {
    if (sseClients.size > 0) {
      // Send channel data directly without battery levels
      // Battery levels are only sent on initial connection and test start
      sendSSEToAllClients(channelsData);
    }
  }

  // Generate CSV report
  generateReport() {
    if (this.devices.size === 0) {
      console.log('No device data to report');
      return;
    }

    // Create filename with date and device type
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const filename = `${date}_12V3A2C.csv`;
    const reportPath = path.join(reportsDir, filename);

    // Check if file exists to determine if we need to write headers
    const fileExists = fs.existsSync(reportPath);

    // Define CSV header
    const csvWriter = createCsvWriter({
      path: reportPath,
      header: [
        { id: 'deviceID', title: 'Device ID' },
        { id: 'serialNumber', title: 'Serial Number' },
        { id: 'chargingStatus', title: 'Charging Status' },
        { id: 'dischargingStatus', title: 'Discharging Status' },
        { id: 'chargingAndDischargingStatus', title: 'Charging And Discharging Status' },
        { id: 'chargingVoltage', title: 'Charging Voltage (V)' },
        { id: 'chargingCurrent', title: 'Charging Current (A)' },
        { id: 'dischargingVoltage', title: 'Discharging Voltage (V)' },
        { id: 'dischargingCurrent', title: 'Discharging Current (A)' },
        { id: 'batteryLevel', title: 'Battery Level (%)' },
      ],
      // Append to existing file if it exists
      append: fileExists,
    });

    // Convert devices map to array for CSV writing
    // Only include devices with PCBs and format the data for CSV
    const records = Array.from(this.devices.values())
      .filter(device => device && this.hasPCB[device.deviceID - 1])
      .map(device => {
        // Create a new object without testID and timestamp
        const { testID, timestamp, ...deviceData } = device;

        // Convert boolean values to proper format (true, false, or "NA")
        // For states that didn't occur, mark as "NA" instead of false
        const formattedDevice = {
          ...deviceData,
          // For chargingStatus, if the device was never in charging state, mark as "NA"
          chargingStatus: this.stateOccurred[DEVICE_STATE.CHARGING]
            ? deviceData.chargingStatus
            : 'NA',

          // For dischargingStatus, if the device was never in discharging state, mark as "NA"
          dischargingStatus: this.stateOccurred[DEVICE_STATE.DISCHARGING]
            ? deviceData.dischargingStatus
            : 'NA',

          // For chargingAndDischargingStatus, if the device was never in combined state, mark as "NA"
          chargingAndDischargingStatus: this.stateOccurred[DEVICE_STATE.CHARGING_AND_DISCHARGING]
            ? deviceData.chargingAndDischargingStatus
            : 'NA',
        };

        return formattedDevice;
      });

    // Calculate success and failure PCBs for this test
    let successCount = 0;
    let failureCount = 0;

    records.forEach(device => {
      // A device is considered successful if it passed all the states that occurred
      // For states that didn't occur, the status is "NA" and doesn't affect success/failure
      const isSuccess =
        (device.chargingStatus === 'NA' || device.chargingStatus === true) &&
        (device.dischargingStatus === 'NA' || device.dischargingStatus === true) &&
        (device.chargingAndDischargingStatus === 'NA' ||
          device.chargingAndDischargingStatus === true);

      if (isSuccess) {
        successCount++;
      } else {
        failureCount++;
      }
    });

    // Update test statistics
    this.testStats.pcbsTested += records.length;
    this.testStats.successPcbs += successCount;
    this.testStats.failurePcbs += failureCount;

    // Save updated stats to cache
    this.saveCachedTestStats();

    // Write CSV file
    csvWriter
      .writeRecords(records)
      .then(() => {
        console.log(`Report updated: ${reportPath}`);
        console.log(`Added ${records.length} device records to the report`);
        console.log(`Test results: ${successCount} success, ${failureCount} failure`);
        console.log(`Updated test stats: ${JSON.stringify(this.testStats)}`);

        // Send test completion message - no longer send testInProgress property
        // Clients determine state from message order
        sendSSEToAllClients({
          testStats: this.testStats,
        });

        // Reset devices for the next test
        this.devices.clear();
        this.testInProgress = false;
      })
      .catch(error => {
        console.error('Error generating report:', error);

        // If there was an error, try to recalculate test stats from the report
        this.recalculateTestStatsFromReport(reportPath);
      });
  }

  // Recalculate test statistics from the CSV report
  recalculateTestStatsFromReport(reportPath) {
    console.log(`Recalculating test statistics from report: ${reportPath}`);

    try {
      // Check if the report file exists
      if (!fs.existsSync(reportPath)) {
        console.log('Report file does not exist, cannot recalculate test statistics');
        return;
      }

      // Parse the CSV file
      const { headers, data } = parseCSVFile(reportPath);

      // Check if we have data
      if (data.length === 0) {
        console.log(
          'Report file is empty or contains only header, cannot recalculate test statistics'
        );
        return;
      }

      // Find the required columns
      const requiredColumns = [
        'Serial Number',
        'Charging Status',
        'Discharging Status',
        'Charging And Discharging Status',
      ];

      // Check if all required columns exist
      const missingColumns = requiredColumns.filter(col => !headers.includes(col));
      if (missingColumns.length > 0) {
        console.log(`Required columns not found: ${missingColumns.join(', ')}`);
        return;
      }

      // Save the current duration
      const currentDuration = this.testStats.duration;

      // Reset test statistics but preserve duration
      this.testStats = {
        panelsTested: 0,
        pcbsTested: 0,
        successPcbs: 0,
        failurePcbs: 0,
        duration: currentDuration, // Preserve the duration
      };

      this.testedPanels = new Set();

      // Process each data row
      data.forEach(row => {
        // Extract values
        const serialNumber = row['Serial Number'];
        const chargingStatus = row['Charging Status'];
        const dischargingStatus = row['Discharging Status'];
        const chargingAndDischargingStatus = row['Charging And Discharging Status'];

        // Add to tested panels
        if (serialNumber) {
          this.testedPanels.add(serialNumber);
        }

        // Count as tested PCB
        this.testStats.pcbsTested++;

        // Determine success or failure
        const isSuccess =
          (chargingStatus === 'NA' || chargingStatus === 'true') &&
          (dischargingStatus === 'NA' || dischargingStatus === 'true') &&
          (chargingAndDischargingStatus === 'NA' || chargingAndDischargingStatus === 'true');

        if (isSuccess) {
          this.testStats.successPcbs++;
        } else {
          this.testStats.failurePcbs++;
        }
      });

      // Update panels tested count
      this.testStats.panelsTested = this.testedPanels.size;

      console.log(`Recalculated test statistics: ${JSON.stringify(this.testStats)}`);

      // Save updated stats to cache
      this.saveCachedTestStats();
    } catch (error) {
      console.error('Error recalculating test statistics:', error);
    }
  }
}

// Create device monitor instance
const deviceMonitor = new DeviceMonitor();

// Set up periodic cache saving (adjust frequency based on environment)
const cacheInterval = process.env.NODE_ENV === 'development' ? 10 * 60 * 1000 : 5 * 60 * 1000; // 10 min dev, 5 min prod
setInterval(() => {
  try {
    deviceMonitor.checkDayChange();
    deviceMonitor.updateDuration();
    deviceMonitor.saveCachedTestStats();
    console.log('Periodic cache save completed');
  } catch (error) {
    console.error('Error during periodic cache save:', error);
  }
}, cacheInterval);

// Calculate test statistics for today's report on startup
function calculateInitialTestStats() {
  console.log("Calculating initial test statistics from today's report...");

  try {
    // Check if reports directory exists
    if (!fs.existsSync(reportsDir)) {
      console.log('Reports directory does not exist, skipping initial test statistics calculation');
      return;
    }

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    const filename = `${today}_12V3A2C.csv`;
    const reportPath = path.join(reportsDir, filename);

    // Check if today's report exists
    if (fs.existsSync(reportPath)) {
      console.log(`Found today's report: ${filename}`);
      deviceMonitor.recalculateTestStatsFromReport(reportPath);
      console.log('Initial test statistics calculation completed');
    } else {
      console.log(
        `No report found for today (${today}), skipping initial test statistics calculation`
      );
    }
  } catch (error) {
    console.error('Error calculating initial test statistics:', error);
  }
}

// Calculate initial test statistics on startup
calculateInitialTestStats();

// Setup API server to expose test statistics
const app = express();
app.use(cors());

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// API endpoint to get test statistics for specific date or today
app.get('/api/test-stats', (req, res) => {
  try {
    const date = req.query.date; // Get date from query parameter
    const reportName = req.query.report; // Get specific report name from query parameter

    // Use provided date or default to today
    const targetDate = date || new Date().toISOString().split('T')[0];
    const isToday = targetDate === new Date().toISOString().split('T')[0];

    // If a specific report is requested, use that one
    if (reportName) {
      const reportPath = path.join(reportsDir, `${reportName}.csv`);
      if (fs.existsSync(reportPath)) {
        const tempMonitor = new DeviceMonitor();
        tempMonitor.recalculateTestStatsFromReport(reportPath);
        const stats = { ...tempMonitor.testStats };
        if (!isToday) delete stats.duration;
        return res.json(stats);
      } else {
        return res.status(404).json({ error: `Report ${reportName}.csv not found` });
      }
    }

    // Check for date's standard report
    const filename = `${targetDate}_12V3A2C.csv`;
    const reportPath = path.join(reportsDir, filename);

    if (fs.existsSync(reportPath)) {
      const tempMonitor = new DeviceMonitor();
      tempMonitor.recalculateTestStatsFromReport(reportPath);
      const stats = { ...tempMonitor.testStats };
      if (!isToday) delete stats.duration;
      return res.json(stats);
    } else {
      // If no report exists for the date, return zeros
      const response = {
        panelsTested: 0,
        pcbsTested: 0,
        successPcbs: 0,
        failurePcbs: 0,
      };
      // Only include duration if it's today's report
      if (isToday) {
        deviceMonitor.checkDayChange();
        deviceMonitor.updateDuration();
        response.duration = deviceMonitor.testStats.duration;
      }
      return res.json(response);
    }
  } catch (error) {
    console.error('Error calculating test stats:', error);
    const response = {
      panelsTested: 0,
      pcbsTested: 0,
      successPcbs: 0,
      failurePcbs: 0,
    };
    if (date === undefined) {
      response.duration = 0;
    }
    return res.status(500).json(response);
  }
});

// Helper function to read and parse a CSV report file
// This function is used by the recalculateTestStatsFromReport method
function parseCSVFile(reportPath) {
  // Read the CSV file
  const fileContent = fs.readFileSync(reportPath, 'utf8');

  // Parse the CSV data
  const lines = fileContent.split('\n');
  const headers = lines[0].split(',').map(header => header.trim());

  const data = [];
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    const values = line.split(',').map(value => value.trim());
    const row = {};

    headers.forEach((header, index) => {
      if (index < values.length) {
        row[header] = values[index];
      }
    });

    data.push(row);
  }

  return { headers, data };
}

// API endpoint to get a list of reports by date (JSON format)
app.get('/api/reports/:date', (req, res) => {
  try {
    const date = req.params.date;

    // Validate date format (YYYY-MM-DD)
    if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // Check if reports directory exists
    if (!fs.existsSync(reportsDir)) {
      return res.status(404).json({ error: 'Reports directory not found' });
    }

    // Get all files in the reports directory
    const files = fs.readdirSync(reportsDir);

    // Filter files that match the date pattern (starts with the date)
    const matchingFiles = files.filter(
      file => file.startsWith(`${date}_`) && file.endsWith('.csv')
    );

    // If no matching files found
    if (matchingFiles.length === 0) {
      return res.status(404).json({ error: `No reports found for date ${date}` });
    }

    // Extract filenames without the .csv extension
    const filenames = matchingFiles.map(file => file.replace('.csv', ''));

    // Return the date and filenames array
    res.json({
      date,
      filename: filenames,
    });
  } catch (error) {
    console.error('Error getting reports by date:', error);
    res.status(500).json({ error: 'Failed to get reports' });
  }
});

// API endpoint to get all serial numbers in a report for a specific date
app.get('/api/reports/:date/all', (req, res) => {
  try {
    const date = req.params.date;

    // Validate date format (YYYY-MM-DD)
    if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // If no report name is provided, use the default format
    const filename = `${date}_12V3A2C.csv`;
    const reportPath = path.join(reportsDir, filename);

    // Check if the report file exists
    if (!fs.existsSync(reportPath)) {
      return res.status(404).json({ error: `Report file ${filename} not found for date ${date}` });
    }

    // Read the CSV file
    const fileContent = fs.readFileSync(reportPath, 'utf8');
    const rows = fileContent.trim().split('\n');
    const headers = rows[0].split(',');

    // Find the index of the Serial Number column
    const serialNumberIndex = headers.findIndex(header => header.trim() === 'Serial Number');

    if (serialNumberIndex === -1) {
      return res.status(500).json({ error: 'Serial Number column not found in the report' });
    }

    // Extract all serial numbers (skip header row)
    const serialNumbers = new Set();
    for (let i = 1; i < rows.length; i++) {
      const columns = rows[i].split(',');
      if (columns.length > serialNumberIndex) {
        serialNumbers.add(columns[serialNumberIndex].trim());
      }
    }

    // Return the unique serial numbers
    return res.json({
      date,
      serialNumbers: Array.from(serialNumbers),
    });
  } catch (error) {
    console.error('Error getting serial numbers from report:', error);
    return res.status(500).json({ error: 'Failed to get serial numbers from report' });
  }
});

// API endpoint to download a CSV report file by date and filename
app.get('/api/reports/:date/download', (req, res) => {
  try {
    const date = req.params.date;
    const reportName = req.query.report; // Get the specific report name from query parameter

    // Validate date format (YYYY-MM-DD)
    if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // If no specific report name is provided, default to the standard format
    const filename = reportName ? `${reportName}.csv` : `${date}_12V3A2C.csv`;
    const reportPath = path.join(reportsDir, filename);

    // Check if the report file exists
    if (!fs.existsSync(reportPath)) {
      return res.status(404).json({ error: `Report file ${filename} not found for date ${date}` });
    }

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Stream the file to the response
    const fileStream = fs.createReadStream(reportPath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error downloading report by date:', error);
    res.status(500).json({ error: 'Failed to download report' });
  }
});

// API endpoint to get report data for a specific serial number
app.get('/api/reports/:date/:serialNumber', (req, res) => {
  try {
    const date = req.params.date;
    const serialNumber = req.params.serialNumber;

    // Validate date format (YYYY-MM-DD)
    if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // If no report name is provided, use the default format
    const filename = `${date}_12V3A2C.csv`;
    const reportPath = path.join(reportsDir, filename);

    // Check if the report file exists
    if (!fs.existsSync(reportPath)) {
      return res.status(404).json({ error: `Report file ${filename} not found for date ${date}` });
    }

    // Read the CSV file
    const fileContent = fs.readFileSync(reportPath, 'utf8');
    const rows = fileContent.trim().split('\n');
    const headers = rows[0].split(',');

    // Find the indices of all required columns
    const columnIndices = {
      'Device ID': headers.findIndex(header => header.trim() === 'Device ID'),
      'Serial Number': headers.findIndex(header => header.trim() === 'Serial Number'),
      'Charging Status': headers.findIndex(header => header.trim() === 'Charging Status'),
      'Discharging Status': headers.findIndex(header => header.trim() === 'Discharging Status'),
      'Charging And Discharging Status': headers.findIndex(
        header => header.trim() === 'Charging And Discharging Status'
      ),
      'Charging Voltage (V)': headers.findIndex(header => header.trim() === 'Charging Voltage (V)'),
      'Charging Current (A)': headers.findIndex(header => header.trim() === 'Charging Current (A)'),
      'Discharging Voltage (V)': headers.findIndex(
        header => header.trim() === 'Discharging Voltage (V)'
      ),
      'Discharging Current (A)': headers.findIndex(
        header => header.trim() === 'Discharging Current (A)'
      ),
      'Battery Level (%)': headers.findIndex(header => header.trim() === 'Battery Level (%)'),
    };

    // Check if required columns exist
    if (columnIndices['Serial Number'] === -1) {
      return res.status(500).json({ error: 'Serial Number column not found in the report' });
    }

    // Filter rows by serial number
    const filteredRows = [];
    for (let i = 1; i < rows.length; i++) {
      const columns = rows[i].split(',');
      if (
        columns.length > columnIndices['Serial Number'] &&
        columns[columnIndices['Serial Number']].trim() === serialNumber
      ) {
        filteredRows.push(columns);
      }
    }

    // If no matching rows found
    if (filteredRows.length === 0) {
      return res
        .status(404)
        .json({ error: `No data found for serial number ${serialNumber} in report ${filename}` });
    }

    // Convert to the requested JSON format
    const formattedData = filteredRows.map(row => {
      // Create the base device data object
      const deviceData = {
        'ID': row[columnIndices['Device ID']].trim(),
        'ChargingStatus': row[columnIndices['Charging Status']].trim(),
        'DischargingStatus': row[columnIndices['Discharging Status']].trim(),
        'ChargingVoltage': row[columnIndices['Charging Voltage (V)']].trim(),
        'ChargingCurrent': row[columnIndices['Charging Current (A)']].trim(),
        'DischargingVoltage': row[columnIndices['Discharging Voltage (V)']].trim(),
        'DischargingCurrent': row[columnIndices['Discharging Current (A)']].trim(),
        'bp': row[columnIndices['Battery Level (%)']].trim(),
      };

      // Only add ChargingDischargingStatus if it's not "NA"
      const chargingDischargingStatus =
        row[columnIndices['Charging And Discharging Status']].trim();
      if (chargingDischargingStatus !== 'NA') {
        // Add as a property to the object
        Object.assign(deviceData, { ChargingDischargingStatus: chargingDischargingStatus });
      }

      return deviceData;
    });

    // Return the formatted data
    return res.json({
      date,
      serialNumber,
      data: formattedData,
    });
  } catch (error) {
    console.error('Error getting report data for serial number:', error);
    return res.status(500).json({ error: 'Failed to get report data for serial number' });
  }
});

// Helper function to send SSE data to all connected clients
function sendSSEToAllClients(data) {
  sseClients.forEach(client => {
    client.write(`data: ${JSON.stringify(data)}\n\n`);
  });
}

// SSE endpoint for real-time test data
app.get('/api/sse/test-data', (req, res) => {
  // Set headers for SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  // No initial connection message - only send actual data

  // Send initial data on client connection
  if (deviceMonitor) {
    // Check if day has changed and update duration
    deviceMonitor.checkDayChange();
    deviceMonitor.updateDuration();

    let initialData;

    // If test is in progress, send all data
    if (deviceMonitor.testInProgress) {
      // Prepare device data in array format
      const deviceArrays = deviceMonitor.prepareDeviceArrays();

      // Create the base message with common properties
      // Include PCB status and battery levels when test is running
      initialData = {
        testStats: deviceMonitor.testStats,
        pcb_status: deviceMonitor.hasPCB, // Only sent when test is running
        bp: deviceMonitor.batteryLevels,
        serialNumber: deviceMonitor.currentPanelSerialNumber,
      };

      // Add state-specific properties only if the state has occurred
      if (
        deviceMonitor.currentState === DEVICE_STATE.CHARGING ||
        deviceMonitor.stateOccurred[DEVICE_STATE.CHARGING]
      ) {
        if (deviceArrays.chargingStatus) initialData.chargingStatus = deviceArrays.chargingStatus;
        if (deviceArrays.chargingVoltage)
          initialData.chargingVoltage = deviceArrays.chargingVoltage;
        if (deviceArrays.chargingCurrent)
          initialData.chargingCurrent = deviceArrays.chargingCurrent;
      }

      if (
        deviceMonitor.currentState === DEVICE_STATE.DISCHARGING ||
        deviceMonitor.stateOccurred[DEVICE_STATE.DISCHARGING]
      ) {
        if (deviceArrays.dischargingStatus)
          initialData.dischargingStatus = deviceArrays.dischargingStatus;
        if (deviceArrays.dischargingVoltage)
          initialData.dischargingVoltage = deviceArrays.dischargingVoltage;
        if (deviceArrays.dischargingCurrent)
          initialData.dischargingCurrent = deviceArrays.dischargingCurrent;
      }

      if (
        deviceMonitor.currentState === DEVICE_STATE.CHARGING_AND_DISCHARGING ||
        deviceMonitor.stateOccurred[DEVICE_STATE.CHARGING_AND_DISCHARGING]
      ) {
        if (deviceArrays.chargingAndDischargingStatus) {
          initialData.chargingAndDischargingStatus = deviceArrays.chargingAndDischargingStatus;
        }
      }

      // Always include progress
      if (deviceArrays.progress) initialData.progress = deviceArrays.progress;
    } else {
      // When no test is running, only send test stats and battery levels
      // Do NOT send PCB status when no test is running - clients determine state from message order
      initialData = {
        testStats: deviceMonitor.testStats,
        bp: deviceMonitor.batteryLevels, // Include battery levels on initial connection
      };
    }

    res.write(`data: ${JSON.stringify(initialData)}\n\n`);
  }

  // Add this client to the set of active clients
  sseClients.add(res);

  // Handle client disconnect
  req.on('close', () => {
    console.log('SSE client disconnected');
    sseClients.delete(res);
  });
});

// Start API server
const API_PORT = process.env.API_PORT || 3001;
app.listen(API_PORT, () => {
  console.log(`API server listening on port ${API_PORT}`);
});

// Connect to MQTT broker
console.log(`Connecting to MQTT broker at ${MQTT_CONFIG.url}...`);
const client = mqtt.connect(MQTT_CONFIG.url, MQTT_CONFIG.options);

// Handle connection events
client.on('connect', () => {
  console.log('Connected to MQTT broker');
  client.subscribe(MQTT_CONFIG.topic, err => {
    if (err) {
      console.error('Error subscribing to topic:', err);
    } else {
      console.log(`Subscribed to topic: ${MQTT_CONFIG.topic}`);
    }
  });
});

client.on('error', err => {
  console.error('MQTT connection error:', err);
});

client.on('close', () => {
  console.log('MQTT connection closed');
});

// Handle incoming messages
client.on('message', (topic, message) => {
  try {
    const messageStr = message.toString().trim();
    console.log(`Received message on topic ${topic}: ${messageStr.substring(0, 100)}...`);

    let parsedMessage;
    try {
      parsedMessage = JSON.parse(messageStr);
    } catch (parseError) {
      console.error('JSON Parse error:', parseError);
      return;
    }

    // Handle PCB status message
    if (Array.isArray(parsedMessage.bp) && Array.isArray(parsedMessage.pcb_status)) {
      deviceMonitor.processPCBStatusMessage(parsedMessage);
      return;
    }

    // Handle state message
    if (parsedMessage.state !== undefined) {
      deviceMonitor.processStateMessage(parsedMessage);
      return;
    }

    // Handle channel data message (array of channel objects)
    if (Array.isArray(parsedMessage)) {
      // Check if this is an array of channel objects
      const isChannelDataArray =
        parsedMessage.length > 0 &&
        parsedMessage.every(
          item =>
            typeof item === 'object' &&
            item !== null &&
            typeof item.channel === 'number' &&
            Array.isArray(item.progress) &&
            Array.isArray(item.elapsed_time)
        );

      if (isChannelDataArray) {
        deviceMonitor.processChannelDataMessage(parsedMessage);
        return;
      }
    }

    // Handle single channel data message (legacy support)
    if (
      typeof parsedMessage === 'object' &&
      parsedMessage !== null &&
      typeof parsedMessage.channel === 'number' &&
      Array.isArray(parsedMessage.progress) &&
      Array.isArray(parsedMessage.elapsed_time)
    ) {
      deviceMonitor.processChannelDataMessage([parsedMessage]);
      return;
    }

    console.log('Message did not match any known format');
  } catch (error) {
    console.error('Error processing MQTT message:', error);
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Disconnecting from MQTT broker...');

  // Save cache before shutting down
  try {
    deviceMonitor.saveCachedTestStats();
    console.log('Cache saved successfully before shutdown');
  } catch (error) {
    console.error('Error saving cache during shutdown:', error);
  }

  client.end();
  process.exit();
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, saving cache and shutting down gracefully...');

  // Save cache before shutting down
  try {
    deviceMonitor.saveCachedTestStats();
    console.log('Cache saved successfully before shutdown');
  } catch (error) {
    console.error('Error saving cache during shutdown:', error);
  }

  client.end();
  process.exit();
});

console.log('MQTT monitoring service started');
