# Qualifuse MQTT Monitoring Service

A comprehensive Node.js service that monitors MQTT messages from the Qualifuse testing system, generates CSV reports, provides real-time data streaming, and maintains persistent test statistics with caching.

## 🚀 Features

### Core Functionality

- **MQTT Integration**: Connects to MQTT broker and monitors `MQTT_TOPIC` topic
- **Real-time Monitoring**: Tracks device data, PCB status, and battery levels during tests
- **Automated Reporting**: Generates daily CSV reports when tests complete (state 4)
- **Test Statistics**: Calculates and maintains comprehensive test metrics
- **Data Persistence**: Robust caching system ensures data survives server restarts

### Advanced Features

- **Server-Sent Events (SSE)**: Real-time data streaming to clients
- **RESTful API**: Comprehensive endpoints for data access and report management
- **Smart Caching**: Persistent test statistics with automatic cleanup
- **Development Tools**: Helper scripts and optimized development workflow
- **Environment Awareness**: Different behaviors for development vs production

### Data Management

- **Daily Reports**: Single CSV file per day (`YYYY-MM-DD_12V3A2C.csv`)
- **Serial Number Tracking**: Groups devices by panel serial numbers
- **State Management**: Tracks charging, discharging, and combined states
- **Success/Failure Analysis**: Automatic calculation of test results
- **Duration Tracking**: Monitors total testing time per day

## 📦 Installation

1. **Prerequisites**: Node.js v14 or later
2. **Install dependencies**:

```bash
npm install
```

3. **Environment setup**: Copy and configure `.env` file:

```bash
cp .env.example .env
# Edit .env with your configuration
```

## ⚙️ Configuration

### Environment Variables (`.env`)

```env
# MQTT Configuration
MQTT_URL=ws://*************:9001/
MQTT_USERNAME=resonate
MQTT_PASSWORD=Resonate@123
MQTT_TOPIC=pico/testing

# Server Configuration
API_PORT=3001
BASE_URL=http://localhost:3001

# File System Configuration
REPORTS_DIR=./reports
CACHE_DIR=./cache
```

### Directory Structure

```
server/
├── cache/                    # Test statistics cache (auto-created)
├── reports/                  # Daily CSV reports (auto-created)
├── public/                   # Static files (auto-created)
├── mqtt-monitor.js          # Main service file
├── .env                     # Environment configuration
└── package.json            # Dependencies and scripts
```

## 🚀 Usage

### Production Mode

```bash
npm start
# or
npm run prod
```

### Development Mode

```bash
# Standard development (frequent restarts)
npm run dev

# Stable development (fewer restarts, recommended)
npm run dev:stable
```

### Development Tools

```bash
# View cache information
npm run cache:info

# Clear cache files
npm run cache:clear

# Check system status
npm run status

# View today's reports
npm run reports:today
```

## 📊 Data Management

### CSV Report Format

Daily reports are generated with the following columns:

| Column                          | Description                | Values                     |
| ------------------------------- | -------------------------- | -------------------------- |
| Device ID                       | Device identifier (1-9)    | Integer                    |
| Serial Number                   | Panel serial number        | String                     |
| Charging Status                 | State 1 result             | `true`, `false`, or `"NA"` |
| Discharging Status              | State 2 result             | `true`, `false`, or `"NA"` |
| Charging And Discharging Status | State 3 result             | `true`, `false`, or `"NA"` |
| Charging Voltage (V)            | Voltage during charging    | Float (2 decimal places)   |
| Charging Current (A)            | Current during charging    | Float (3 decimal places)   |
| Discharging Voltage (V)         | Voltage during discharging | Float (2 decimal places)   |
| Discharging Current (A)         | Current during discharging | Float (3 decimal places)   |
| Battery Level (%)               | Battery percentage         | Integer                    |

### Test Statistics

The system tracks comprehensive statistics:

```json
{
  "panelsTested": 5, // Unique serial numbers tested
  "pcbsTested": 45, // Total devices tested
  "successPcbs": 42, // Devices that passed all states
  "failurePcbs": 3, // Devices that failed any state
  "duration": 7200 // Total testing time in seconds
}
```

### Caching System

- **Persistent Storage**: Test stats cached to `cache/teststats_YYYY-MM-DD.json`
- **Automatic Recovery**: Data restored on server restart
- **Smart Cleanup**: Old cache files (7+ days) automatically removed
- **Development Mode**: Stale cache (10+ minutes) ignored during development

## 🔌 API Endpoints

### Test Statistics

#### Get Current Test Statistics

```http
GET /api/test-stats
GET /api/test-stats?date=YYYY-MM-DD
```

Returns comprehensive test statistics:

```json
{
  "panelsTested": 5,
  "pcbsTested": 45,
  "successPcbs": 42,
  "failurePcbs": 3,
  "duration": 7200
}
```

**Parameters:**

- `date` (optional): Specific date in YYYY-MM-DD format
- `report` (optional): Specific report name

**Notes:**

- Duration only included for today's statistics
- Returns zeros if no data exists for the specified date

### Real-time Data Streaming

#### Server-Sent Events (SSE)

```http
GET /api/sse/test-data
```

Streams real-time test data to clients with different message types:

**Initial Connection:**

```json
{
  "testStats": { "panelsTested": 5, "pcbsTested": 45, ... },
  "bp": [85, 90, 88, ...],
  "pcb_status": [true, true, false, ...],
  "serialNumber": "SN-12345"
}
```

**During Tests:**

```json
{
  "state": 1,
  "chargingStatus": [true, false, true, ...],
  "chargingVoltage": [12.5, 0, 12.3, ...],
  "chargingCurrent": [2.1, 0, 2.0, ...]
}
```

**Test Completion:**

```json
{
  "testStats": { "panelsTested": 6, "pcbsTested": 54, ... }
}
```

**Client Example:**

```javascript
const eventSource = new EventSource('/api/sse/test-data');
eventSource.onmessage = event => {
  const data = JSON.parse(event.data);

  if (data.pcb_status) {
    // Test starting - update device status
    updateDeviceCards(data);
  } else if (data.state) {
    // State change during test
    updateTestState(data);
  } else if (data.testStats && !data.pcb_status) {
    // Test completed
    updateFinalStats(data.testStats);
  }
};
```

### Report Management

#### Get Available Reports

```http
GET /api/reports/:date
```

Returns list of available reports for a specific date:

```json
{
  "date": "2025-01-27",
  "filename": ["2025-01-27_12V3A2C"]
}
```

#### Get Serial Numbers

```http
GET /api/reports/:date/all
```

Returns all serial numbers in a report:

```json
{
  "date": "2025-01-27",
  "serialNumbers": ["SN-001", "SN-002", "SN-003"]
}
```

#### Get Report Data by Serial Number

```http
GET /api/reports/:date/:serialNumber
```

Returns filtered data for a specific serial number:

```json
{
  "date": "2025-01-27",
  "serialNumber": "SN-001",
  "data": [
    {
      "ID": "1",
      "ChargingStatus": "true",
      "DischargingStatus": "true",
      "ChargingVoltage": "12.50",
      "ChargingCurrent": "2.100",
      "bp": "85"
    }
  ]
}
```

#### Download Reports

```http
GET /api/reports/today/download
GET /api/reports/:date/download
GET /api/reports/:date/download?report=custom_name
```

Downloads CSV reports with proper headers for file download.

## 🛠️ Development & Troubleshooting

### Development Workflow

1. **Start in stable mode**: `npm run dev:stable`
2. **Monitor logs**: Watch console for cache loading/saving messages
3. **Check status**: `npm run status` to verify system state
4. **Clear cache when needed**: `npm run cache:clear`

### Common Issues

#### Nodemon Restart Issues

```bash
# Use stable development mode
npm run dev:stable

# Manual restart if needed
rs  # (in nodemon terminal)
```

#### Cache Problems

```bash
# View cache information
npm run cache:info

# Clear stale cache
npm run cache:clear

# Check system status
npm run status
```

#### MQTT Connection Issues

1. Verify MQTT broker is running and accessible
2. Check `.env` configuration
3. Verify network connectivity to broker
4. Check console logs for connection errors

#### Missing Reports

```bash
# Check if reports directory exists
npm run reports:view

# View today's report status
npm run reports:today
```

### Performance Monitoring

- **Cache saves**: Monitor console for "Test stats cached" messages
- **Memory usage**: Check for memory leaks during long-running tests
- **File system**: Monitor disk space for reports and cache directories
- **MQTT messages**: Watch for message processing errors

### Production Deployment

1. **Set environment**: `NODE_ENV=production`
2. **Use production mode**: `npm run prod`
3. **Monitor cache**: Ensure cache directory is writable
4. **Backup reports**: Regular backup of reports directory
5. **Log rotation**: Implement log rotation for long-term operation

For detailed development information, see [DEVELOPMENT.md](./DEVELOPMENT.md).
