/**
 * Centralized API configuration
 * All API URLs and endpoints should be defined here using environment variables
 */

// Base API URL from environment variable
const BASE_API_URL = process.env.NEXT_PUBLIC_API_URL;
const SSE_URL = process.env.NEXT_PUBLIC_SSE_URL;

// Validate required environment variables
if (!BASE_API_URL) {
  throw new Error('NEXT_PUBLIC_API_URL environment variable is required');
}

if (!SSE_URL) {
  throw new Error('NEXT_PUBLIC_SSE_URL environment variable is required');
}

/**
 * API Configuration object containing all endpoints
 */
export const API_CONFIG = {
  // Base URL
  BASE_URL: BASE_API_URL,

  // SSE Configuration
  SSE: {
    TEST_DATA: SSE_URL,
  },

  // API Endpoints
  ENDPOINTS: {
    // Test Statistics
    TEST_STATS: (date?: string) =>
      date ? `${BASE_API_URL}/api/test-stats?date=${date}` : `${BASE_API_URL}/api/test-stats`,

    // Reports
    REPORTS: {
      // Get reports for a specific date
      BY_DATE: (date: string) => `${BASE_API_URL}/api/reports/${date}`,

      // Get report data for specific serial number and date
      BY_DATE_AND_SERIAL: (date: string, serialNumber: string) =>
        `${BASE_API_URL}/api/reports/${date}/${serialNumber}`,

      // Get all serial numbers for a specific date
      SERIAL_NUMBERS_BY_DATE: (date: string) => `${BASE_API_URL}/api/reports/${date}/all`,

      // Download report by date
      DOWNLOAD_BY_DATE: (date: string, reportName?: string) =>
        reportName
          ? `${BASE_API_URL}/api/reports/${date}/download?report=${reportName}`
          : `${BASE_API_URL}/api/reports/${date}/download`,

      // Download today's report
      DOWNLOAD_TODAY: () => `${BASE_API_URL}/api/reports/today/download`,
    },
  },
} as const;

/**
 * Utility function to get the base API URL
 */
export const getBaseApiUrl = (): string => BASE_API_URL;

/**
 * Utility function to get the SSE URL
 */
export const getSSEUrl = (): string => SSE_URL;

/**
 * Utility function to build API endpoint URLs
 */
export const buildApiUrl = (endpoint: string): string => {
  // Remove leading slash if present to avoid double slashes
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${BASE_API_URL}/${cleanEndpoint}`;
};

/**
 * Utility function to format date for API calls (YYYY-MM-DD)
 */
export const formatDateForApi = (date: Date): string => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
    date.getDate()
  ).padStart(2, '0')}`;
};

/**
 * Type definitions for API responses
 */
export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

/**
 * Common fetch wrapper with error handling
 */
export const apiRequest = async <T = any>(
  url: string,
  options?: RequestInit
): Promise<ApiResponse<T>> => {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const data = await response.json();
    return {
      success: true,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};
