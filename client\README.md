# Qualifuse Testing Dashboard

A modern, responsive React dashboard for monitoring and managing Qualifuse battery testing operations. Built with Next.js, TypeScript, and Tailwind CSS, providing real-time test monitoring, historical data analysis, and comprehensive reporting capabilities.

## 🚀 Features

### Real-time Monitoring

- **Live Test Dashboard**: Real-time device status monitoring with SSE integration
- **Device Cards**: Visual representation of 9 testing devices with status indicators
- **Battery Level Monitoring**: Real-time battery percentage display with visual indicators
- **State Tracking**: Monitors charging, discharging, and combined test states
- **Progress Indicators**: Visual progress bars for ongoing tests

### Historical Analysis

- **History View**: Browse test results by date and serial number
- **Data Filtering**: Filter results by specific panel serial numbers
- **Test Results**: Pass/fail status with detailed state information
- **Date Navigation**: Easy navigation through historical test data

### Reporting & Downloads

- **CSV Report Downloads**: Download complete daily reports
- **Report Management**: Access reports by date with download functionality
- **Test Statistics**: Comprehensive metrics including success rates and duration
- **Data Export**: Export filtered data for analysis

### User Interface

- **Modern Design**: Clean, professional interface with card-based layout
- **Responsive**: Works seamlessly on desktop, tablet, and mobile devices
- **Dark/Light Themes**: Adaptive theming for different environments
- **Intuitive Navigation**: Easy switching between live and historical views
- **Status Indicators**: Clear visual feedback for connection and test status

## 🛠️ Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS for responsive design
- **UI Components**: Custom components with Lucide React icons
- **State Management**: React hooks and context
- **Real-time Data**: Server-Sent Events (SSE)
- **HTTP Client**: Fetch API with custom configuration
- **Date Handling**: Built-in JavaScript Date API

## 📦 Installation

### Prerequisites

- Node.js 18 or later
- npm or yarn package manager

### Setup

1. **Install dependencies**:

```bash
npm install
# or
yarn install
```

2. **Environment configuration**:

```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

3. **Start development server**:

```bash
npm run dev
# or
yarn dev
```

4. **Open in browser**:

```
http://localhost:3000
```

## ⚙️ Configuration

### Environment Variables (`.env.local`)

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://*************:3001

# Optional: Custom API endpoints
NEXT_PUBLIC_SSE_ENDPOINT=/api/sse/test-data
NEXT_PUBLIC_STATS_ENDPOINT=/api/test-stats
```

### API Configuration (`lib/api-config.ts`)

The application uses a centralized API configuration system:

```typescript
const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001',
  ENDPOINTS: {
    SSE: {
      TEST_DATA: '/api/sse/test-data',
    },
    STATS: {
      CURRENT: '/api/test-stats',
      BY_DATE: (date: string) => `/api/test-stats?date=${date}`,
    },
    REPORTS: {
      BY_DATE: (date: string) => `/api/reports/${date}`,
      DOWNLOAD_BY_DATE: (date: string) => `/api/reports/${date}/download`,
      SERIAL_NUMBERS_BY_DATE: (date: string) => `/api/reports/${date}/all`,
    },
  },
};
```

## 🏗️ Project Structure

```
client/
├── app/                      # Next.js App Router
│   ├── globals.css          # Global styles
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Home page
├── components/              # React components
│   ├── dashboard/           # Dashboard-specific components
│   │   ├── control-panel.tsx    # Main control panel
│   │   ├── device-card.tsx      # Individual device cards
│   │   ├── device-grid.tsx      # Device grid layout
│   │   ├── history-list.tsx     # Historical data list
│   │   └── stats-panel.tsx      # Statistics display
│   └── ui/                  # Reusable UI components
│       ├── card.tsx         # Card component
│       ├── button.tsx       # Button component
│       └── badge.tsx        # Badge component
├── lib/                     # Utility libraries
│   ├── api-config.ts        # API configuration
│   ├── types.ts             # TypeScript type definitions
│   └── utils.ts             # Utility functions
├── hooks/                   # Custom React hooks
│   ├── use-sse.ts          # SSE connection hook
│   └── use-api.ts          # API interaction hook
└── public/                  # Static assets
    └── icons/               # Application icons
```

## 🔌 Real-time Data Integration

### SSE Connection

The dashboard uses Server-Sent Events for real-time data:

```typescript
// Custom SSE hook
const { data, isConnected, error } = useSSE('/api/sse/test-data');

// Data processing
useEffect(() => {
  if (data) {
    if (data.pcb_status) {
      // Test starting - update device status
      updateDeviceCards(data);
    } else if (data.state) {
      // State change during test
      updateTestState(data);
    } else if (data.testStats && !data.pcb_status) {
      // Test completed
      updateFinalStats(data.testStats);
    }
  }
}, [data]);
```

### Data Flow

1. **Initial Connection**: Receives current test stats and device status
2. **Test Start**: PCB status and battery levels received
3. **State Changes**: Real-time updates during test progression
4. **Test Completion**: Final statistics and results
5. **Connection Management**: Automatic reconnection on disconnect

## 📊 Data Types

### Device Data

```typescript
interface DeviceData {
  ID: string;
  ChargingStatus: boolean | string;
  DischargingStatus: boolean | string;
  ChargingDischargingStatus?: boolean | string;
  ChargingVoltage: number;
  ChargingCurrent: number;
  DischargingVoltage: number;
  DischargingCurrent: number;
  bp: number; // Battery percentage
}
```

### Test Statistics

```typescript
interface TestStats {
  panelsTested: number;
  pcbsTested: number;
  successPcbs: number;
  failurePcbs: number;
  duration?: number; // Only for current day
}
```

### SSE Message Types

```typescript
interface SSEMessage {
  testStats?: TestStats;
  pcb_status?: boolean[];
  bp?: number[];
  serialNumber?: string;
  state?: number;
  chargingStatus?: boolean[];
  chargingVoltage?: number[];
  chargingCurrent?: number[];
  dischargingStatus?: boolean[];
  dischargingVoltage?: number[];
  dischargingCurrent?: number[];
  progress?: number[];
}
```

## 🎨 UI Components

### Device Cards

- **Status Indicators**: CheckCircle (pass), AlertCircle (fail), Clock (in progress)
- **Battery Icons**: Dynamic battery level visualization
- **State Colors**: Green (charging), Orange (discharging), Blue (combined)
- **Measurements**: Voltage (2 decimal places), Current (3 decimal places)

### Control Panel

- **View Toggle**: Switch between live and history views
- **Date Picker**: Navigate through historical data
- **Serial Number Filter**: Filter by specific panel serial numbers
- **Download Controls**: Direct report download functionality

### Statistics Panel

- **Real-time Metrics**: Live updating test statistics
- **Success Rate**: Visual percentage indicators
- **Duration Tracking**: Time elapsed for current day
- **Historical Comparison**: Compare different dates

## 🚀 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking

# Testing
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Generate coverage report
```

### Development Workflow

1. **Start development server**: `npm run dev`
2. **Configure API endpoint**: Update `.env.local` with server URL
3. **Monitor console**: Check for SSE connection and data flow
4. **Test features**: Verify live monitoring and history views
5. **Build for production**: `npm run build`

### Code Quality

- **TypeScript**: Strict type checking enabled
- **ESLint**: Code linting with Next.js recommended rules
- **Prettier**: Code formatting (if configured)
- **Tailwind CSS**: Utility-first styling approach

## 🔧 Customization

### Theming

Modify `app/globals.css` for custom themes:

```css
:root {
  --primary-color: #10b981;
  --secondary-color: #f59e0b;
  --background-color: #f8fafc;
  --text-color: #1f2937;
}
```

### API Endpoints

Update `lib/api-config.ts` to modify API endpoints:

```typescript
const API_CONFIG = {
  BASE_URL: 'http://your-server:3001',
  // ... endpoint configurations
};
```

### Device Configuration

Modify device count and layout in `components/dashboard/device-grid.tsx`:

```typescript
const DEVICE_COUNT = 9; // Change number of devices
const GRID_COLS = 3; // Change grid layout
```

## 🐛 Troubleshooting

### Common Issues

#### SSE Connection Problems

```typescript
// Check connection status
console.log('SSE Connected:', isConnected);
console.log('SSE Error:', error);

// Verify API endpoint
console.log('API Base URL:', process.env.NEXT_PUBLIC_API_BASE_URL);
```

#### Data Not Updating

1. Check SSE connection status in browser dev tools
2. Verify server is running and accessible
3. Check console for JavaScript errors
4. Verify API endpoint configuration

#### Build Issues

```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Type check
npm run type-check
```

#### Styling Issues

1. Check Tailwind CSS configuration
2. Verify component class names
3. Check for CSS conflicts
4. Use browser dev tools to inspect styles

### Performance Optimization

- **SSE Connection**: Implement connection pooling for multiple tabs
- **Data Caching**: Cache historical data to reduce API calls
- **Component Optimization**: Use React.memo for expensive components
- **Bundle Analysis**: Use `@next/bundle-analyzer` to optimize bundle size

## 📱 Mobile Support

The dashboard is fully responsive and supports:

- **Touch Navigation**: Optimized for touch devices
- **Responsive Layout**: Adapts to different screen sizes
- **Mobile-first Design**: Progressive enhancement approach
- **Gesture Support**: Swipe navigation where appropriate

## 🔒 Security Considerations

- **Environment Variables**: Sensitive data in environment variables only
- **API Validation**: Client-side validation with server-side verification
- **CORS Configuration**: Proper CORS setup for API access
- **Input Sanitization**: All user inputs properly sanitized

## 📈 Future Enhancements

- **WebSocket Support**: Upgrade from SSE to WebSocket for bidirectional communication
- **Advanced Filtering**: More sophisticated data filtering options
- **Export Formats**: Support for Excel, PDF export formats
- **User Authentication**: Role-based access control
- **Notifications**: Real-time alerts for test failures
- **Data Visualization**: Charts and graphs for trend analysis

For server-side documentation, see [../server/README.md](../server/README.md).
