# Development Guide

## Running the Server

### Production Mode
```bash
npm start
# or
npm run prod
```

### Development Mode

#### Standard Development (with frequent restarts)
```bash
npm run dev
```

#### Stable Development (fewer restarts, better for testing)
```bash
npm run dev:stable
```

## Nodemon Configuration

The server is configured with nodemon to minimize unnecessary restarts during development:

### What Triggers Restarts
- Changes to `mqtt-monitor.js`
- Changes to `.env` file

### What is Ignored
- `cache/` directory (cache files)
- `reports/` directory (CSV reports)
- `node_modules/` directory
- `*.json` files (except package.json)
- `*.csv` files
- `*.log` files
- Test files (`test-*.js`)

### Restart Delay
- **Standard dev**: 2 seconds delay
- **Stable dev**: 5 seconds delay

## Cache Management

### Development vs Production Behavior

#### Development Mode
- Cache is only loaded if it's less than 10 minutes old
- Periodic cache saves every 10 minutes
- Stale cache is ignored to prevent confusion during development

#### Production Mode
- Cache is always loaded regardless of age
- Periodic cache saves every 5 minutes
- Full cache persistence for reliability

### Cache Commands
```bash
# View cache files
npm run cache:view

# Clear all cache files
npm run cache:clear
```

## Troubleshooting Nodemon Issues

### Issue: Too Many Restarts
**Solution**: Use stable development mode
```bash
npm run dev:stable
```

### Issue: Cache Conflicts During Development
**Solution**: Clear cache and restart
```bash
npm run cache:clear
npm run dev:stable
```

### Issue: Nodemon Not Restarting
**Solution**: Manually restart with 'rs'
```bash
# In the nodemon terminal, type:
rs
```

### Issue: File Watching Problems
**Solution**: Check nodemon configuration
```bash
# View current nodemon config
cat nodemon.json
```

## Best Practices for Development

1. **Use stable mode for testing**: `npm run dev:stable`
2. **Clear cache when switching test scenarios**: `npm run cache:clear`
3. **Use production mode for final testing**: `npm run prod`
4. **Monitor cache age in logs** to understand data freshness

## Environment Variables

Set `NODE_ENV=development` for development features:
- Shorter cache validity (10 minutes)
- Less frequent periodic saves
- More verbose logging

## File Structure
```
server/
├── cache/                    # Cache files (ignored by nodemon)
│   └── teststats_*.json
├── reports/                  # CSV reports (ignored by nodemon)
│   └── *.csv
├── mqtt-monitor.js          # Main server file (watched)
├── .env                     # Environment config (watched)
├── nodemon.json            # Nodemon configuration
└── package.json            # Dependencies and scripts
```

## Tips

- Use `rs` in nodemon terminal to manually restart
- Check console logs for cache loading/saving messages
- Cache files are human-readable JSON for debugging
- Old cache files are automatically cleaned up (7+ days)
