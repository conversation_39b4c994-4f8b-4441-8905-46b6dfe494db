#!/usr/bin/env node

/**
 * Development Helper Script
 * Provides utilities for managing the development environment
 */

const fs = require('fs');
const path = require('path');

const cacheDir = './cache';
const reportsDir = './reports';

function showHelp() {
  console.log(`
Development Helper for MQTT Monitor

Usage: node dev-helper.js <command>

Commands:
  cache:view     - View all cache files
  cache:clear    - Clear all cache files
  cache:info     - Show cache information
  reports:view   - View all report files
  reports:today  - Show today's report
  status         - Show overall status
  help           - Show this help message

Examples:
  node dev-helper.js cache:clear
  node dev-helper.js status
`);
}

function viewCache() {
  console.log('\n📁 Cache Files:');
  if (!fs.existsSync(cacheDir)) {
    console.log('   No cache directory found');
    return;
  }

  const files = fs.readdirSync(cacheDir).filter(f => f.endsWith('.json'));
  if (files.length === 0) {
    console.log('   No cache files found');
    return;
  }

  files.forEach(file => {
    const filePath = path.join(cacheDir, file);
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024).toFixed(2);
    console.log(`   ${file} (${size} KB, modified: ${stats.mtime.toLocaleString()})`);
  });
}

function clearCache() {
  console.log('\n🗑️  Clearing cache files...');
  if (!fs.existsSync(cacheDir)) {
    console.log('   No cache directory found');
    return;
  }

  const files = fs.readdirSync(cacheDir).filter(f => f.endsWith('.json'));
  if (files.length === 0) {
    console.log('   No cache files to clear');
    return;
  }

  files.forEach(file => {
    const filePath = path.join(cacheDir, file);
    fs.unlinkSync(filePath);
    console.log(`   Deleted: ${file}`);
  });
  console.log(`   Cleared ${files.length} cache files`);
}

function showCacheInfo() {
  console.log('\n📊 Cache Information:');
  if (!fs.existsSync(cacheDir)) {
    console.log('   No cache directory found');
    return;
  }

  const files = fs.readdirSync(cacheDir).filter(f => f.endsWith('.json'));
  if (files.length === 0) {
    console.log('   No cache files found');
    return;
  }

  files.forEach(file => {
    const filePath = path.join(cacheDir, file);
    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      console.log(`\n   📄 ${file}:`);
      console.log(`      Date: ${data.date}`);
      console.log(`      Panels Tested: ${data.testStats?.panelsTested || 0}`);
      console.log(`      PCBs Tested: ${data.testStats?.pcbsTested || 0}`);
      console.log(`      Success Rate: ${data.testStats?.pcbsTested ? 
        ((data.testStats.successPcbs / data.testStats.pcbsTested) * 100).toFixed(1) : 0}%`);
      console.log(`      Duration: ${data.testStats?.duration || 0} seconds`);
      console.log(`      Last Updated: ${new Date(data.lastUpdated).toLocaleString()}`);
    } catch (error) {
      console.log(`   ❌ Error reading ${file}: ${error.message}`);
    }
  });
}

function viewReports() {
  console.log('\n📋 Report Files:');
  if (!fs.existsSync(reportsDir)) {
    console.log('   No reports directory found');
    return;
  }

  const files = fs.readdirSync(reportsDir).filter(f => f.endsWith('.csv'));
  if (files.length === 0) {
    console.log('   No report files found');
    return;
  }

  files.forEach(file => {
    const filePath = path.join(reportsDir, file);
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024).toFixed(2);
    console.log(`   ${file} (${size} KB, modified: ${stats.mtime.toLocaleString()})`);
  });
}

function showTodayReport() {
  const today = new Date().toISOString().split('T')[0];
  const todayFile = `${today}_12V3A2C.csv`;
  const filePath = path.join(reportsDir, todayFile);

  console.log(`\n📅 Today's Report (${today}):`);
  if (!fs.existsSync(filePath)) {
    console.log('   No report found for today');
    return;
  }

  const stats = fs.statSync(filePath);
  const size = (stats.size / 1024).toFixed(2);
  console.log(`   File: ${todayFile}`);
  console.log(`   Size: ${size} KB`);
  console.log(`   Modified: ${stats.mtime.toLocaleString()}`);

  // Count lines to estimate number of tests
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    console.log(`   Estimated tests: ${Math.max(0, lines.length - 1)} (excluding header)`);
  } catch (error) {
    console.log(`   Error reading file: ${error.message}`);
  }
}

function showStatus() {
  console.log('\n🔍 Development Environment Status:');
  
  // Check directories
  console.log(`   Cache directory: ${fs.existsSync(cacheDir) ? '✅ exists' : '❌ missing'}`);
  console.log(`   Reports directory: ${fs.existsSync(reportsDir) ? '✅ exists' : '❌ missing'}`);
  
  // Check environment
  const envFile = '.env';
  console.log(`   Environment file: ${fs.existsSync(envFile) ? '✅ exists' : '❌ missing'}`);
  
  // Check nodemon config
  const nodemonFile = 'nodemon.json';
  console.log(`   Nodemon config: ${fs.existsSync(nodemonFile) ? '✅ exists' : '❌ missing'}`);
  
  viewCache();
  viewReports();
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'cache:view':
    viewCache();
    break;
  case 'cache:clear':
    clearCache();
    break;
  case 'cache:info':
    showCacheInfo();
    break;
  case 'reports:view':
    viewReports();
    break;
  case 'reports:today':
    showTodayReport();
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  case undefined:
    showHelp();
    break;
  default:
    console.log(`❌ Unknown command: ${command}`);
    showHelp();
    process.exit(1);
}
