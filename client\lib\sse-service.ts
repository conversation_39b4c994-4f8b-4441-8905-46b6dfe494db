'use client';

import { API_CONFIG } from './api-config';

// SSE connection configuration
const SSE_CONFIG = {
  url: API_CONFIG.SSE.TEST_DATA,
};

// Event handler types
type MessageHandler = (message: any) => void;
type ConnectionHandler = (isConnected: boolean) => void;

// SSE Service class
class SSEService {
  private eventSource: EventSource | null = null;
  private isConnected = false;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectInterval = 5000; // 5 seconds
  private messageHandlers: Set<MessageHandler> = new Set();
  private connectionHandlers: Set<ConnectionHandler> = new Set();
  private lastMessageTime = 0;
  private connectionCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Set up a connection check interval to detect stale connections
    this.connectionCheckInterval = setInterval(() => this.checkConnection(), 30000);
  }

  // Connect to SSE endpoint
  connect(): void {
    if (this.isConnected && this.eventSource) {
      return;
    }

    try {
      // Ensure the URL is not undefined before creating EventSource
      if (!SSE_CONFIG.url) {
        throw new Error('SSE URL is not defined. Check your environment variables.');
      }

      this.eventSource = new EventSource(SSE_CONFIG.url);
      this.setupEventHandlers();
    } catch (error) {
      console.error('Error creating SSE connection:', error);
      this.handleDisconnection();
    }
  }

  // Set up event handlers for the EventSource
  private setupEventHandlers(): void {
    if (!this.eventSource) return;

    this.eventSource.onopen = this.handleConnection.bind(this);
    this.eventSource.onmessage = this.handleMessage.bind(this);
    this.eventSource.onerror = this.handleError.bind(this);
  }

  // Handle successful connection
  private handleConnection(): void {
    console.log('Connected to SSE endpoint');
    this.isConnected = true;
    this.lastMessageTime = Date.now();
    this.notifyConnectionHandlers(true);
    this.clearReconnectTimer();
  }

  // Handle incoming message
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      // Update last message time
      this.lastMessageTime = Date.now();

      // If we receive a message, we're definitely connected
      if (!this.isConnected) {
        console.log('Message received while disconnected, updating connection status');
        this.isConnected = true;
        this.notifyConnectionHandlers(true);
      }

      this.notifyMessageHandlers(data);
    } catch (error) {
      console.error('Error parsing SSE message:', error);
    }
  }

  // Handle connection error
  private handleError(): void {
    console.warn('SSE connection error - attempting to reconnect shortly');
    this.handleDisconnection();
    this.scheduleReconnect();
  }

  // Handle disconnection
  private handleDisconnection(): void {
    // Only update status if we were previously connected
    if (this.isConnected) {
      this.isConnected = false;
      this.notifyConnectionHandlers(false);
    }

    // Close the connection
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    if (!this.reconnectTimer) {
      this.reconnectTimer = setTimeout(() => {
        console.log('Attempting to reconnect to SSE...');
        this.connect();
        this.reconnectTimer = null;
      }, this.reconnectInterval);
    }
  }

  // Clear reconnect timer
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  // Check if connection is stale
  private checkConnection(): void {
    if (this.isConnected && Date.now() - this.lastMessageTime > 60000) {
      console.warn('No SSE messages received for 60 seconds, reconnecting...');
      this.handleDisconnection();
      this.scheduleReconnect();
    }
  }

  // Disconnect from SSE endpoint
  disconnect(): void {
    this.handleDisconnection();
    this.clearReconnectTimer();

    // Clear connection check interval
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
      this.connectionCheckInterval = null;
    }
  }

  // Check if connected
  isConnectedToSSE(): boolean {
    return this.isConnected;
  }

  // Add message handler
  onMessage(handler: MessageHandler): void {
    this.messageHandlers.add(handler);
  }

  // Remove message handler
  removeMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.delete(handler);
  }

  // Add connection handler
  onConnectionChange(handler: ConnectionHandler): void {
    this.connectionHandlers.add(handler);
    // Immediately notify with current status
    handler(this.isConnected);
  }

  // Remove connection handler
  removeConnectionHandler(handler: ConnectionHandler): void {
    this.connectionHandlers.delete(handler);
  }

  // Notify all message handlers
  private notifyMessageHandlers(data: any): void {
    this.messageHandlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error('Error in SSE message handler:', error);
      }
    });
  }

  // Notify all connection handlers
  private notifyConnectionHandlers(isConnected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(isConnected);
      } catch (error) {
        console.error('Error in SSE connection handler:', error);
      }
    });
  }
}

// Create singleton instance
const sseService = new SSEService();

export default sseService;
