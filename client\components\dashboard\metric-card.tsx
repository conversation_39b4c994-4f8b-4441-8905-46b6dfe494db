'use client';

import { LucideIcon } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import {
  getCardStyle,
  getStatusCardStyle,
  ActivityType,
  ACTIVITY_TYPE,
  getActivityColor,
} from '@/lib/dashboard-utils';

interface MetricCardProps {
  icon: LucideIcon;
  title: string;
  value: string | number;
  isActive: boolean;
  type: ActivityType;
  isStatus?: boolean;
  activeText?: string;
  inactiveText?: string;
}

export function MetricCard({
  icon: Icon,
  title,
  value,
  isActive,
  type,
  isStatus = false,
  activeText = 'Active',
  inactiveText = 'Inactive',
}: MetricCardProps) {
  // Convert ActivityType to StyleType for backward compatibility
  const styleType =
    type === ACTIVITY_TYPE.INACTIVE
      ? ACTIVITY_TYPE.CHARGING // Default to charging for inactive (will be overridden by isActive=false)
      : type;

  const cardStyleFn = isStatus ? getStatusCardStyle : getCardStyle;
  const activityType = isActive ? type : ACTIVITY_TYPE.INACTIVE;
  const iconColorClass = getActivityColor(activityType, 'icon');
  const textColorClass = isActive
    ? getActivityColor(activityType, 'text')
    : 'text-muted-foreground';

  return (
    <Card
      className={`${cardStyleFn(
        isActive,
        styleType
      )} shadow-sm hover:shadow-md transition-all duration-300 ease-out pcb-card-hover animate-fade-in-up ${
        isActive && type === ACTIVITY_TYPE.CHARGING ? 'pcb-charging' : ''
      } ${isActive && type === ACTIVITY_TYPE.DISCHARGING ? 'pcb-discharging' : ''} ${
        isStatus && isActive ? 'pcb-status-success' : ''
      } ${isStatus && !isActive ? 'pcb-status-warning' : ''}`}
    >
      <CardContent className="flex flex-col items-center justify-center p-6">
        <div className="text-3xl mb-2 icon-transition">
          <Icon className={`h-12 w-12 ${iconColorClass} transition-all duration-300`} />
        </div>
        <h3 className="text-sm font-medium transition-colors duration-200">{title}</h3>
        {isStatus ? (
          <p
            className={`text-lg font-bold ${textColorClass} transition-all duration-300 ${
              isActive ? 'status-indicator success' : 'status-indicator'
            }`}
          >
            {isActive ? activeText : inactiveText}
          </p>
        ) : (
          <p
            className={`text-lg font-bold ${
              isActive ? '' : 'text-muted-foreground'
            } transition-all duration-300 data-transition`}
          >
            {value}
          </p>
        )}
      </CardContent>
    </Card>
  );
}
