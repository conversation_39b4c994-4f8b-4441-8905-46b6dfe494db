'use client';

import React from 'react';
import { CircuitBoard, Battery, Zap, Activity, Cpu, Gauge } from 'lucide-react';

export type LoaderType = 
  | 'pcb-circuit' 
  | 'battery-charging' 
  | 'electronic-wave' 
  | 'pcb-spinner' 
  | 'voltage-meter' 
  | 'data-flow' 
  | 'connection-points' 
  | 'pcb-test-progress';

interface ElectronicsLoaderProps {
  type: LoaderType;
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  className?: string;
}

const sizeClasses = {
  sm: 'scale-75',
  md: 'scale-100',
  lg: 'scale-125',
};

export function ElectronicsLoader({ 
  type, 
  size = 'md', 
  message, 
  className = '' 
}: ElectronicsLoaderProps) {
  const renderLoader = () => {
    switch (type) {
      case 'pcb-circuit':
        return (
          <div className={`pcb-circuit-loader ${sizeClasses[size]}`}>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <CircuitBoard className="h-6 w-6 text-blue-400" />
            </div>
          </div>
        );

      case 'battery-charging':
        return (
          <div className={`battery-charging-loader ${sizeClasses[size]}`}>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <Battery className="h-4 w-4 text-gray-600" />
            </div>
          </div>
        );

      case 'electronic-wave':
        return (
          <div className={`electronic-wave-loader ${sizeClasses[size]}`}>
            <div className="absolute top-1/2 left-2 transform -translate-y-1/2">
              <Activity className="h-3 w-3 text-blue-400" />
            </div>
            <div className="absolute top-1/2 right-2 transform -translate-y-1/2">
              <Activity className="h-3 w-3 text-blue-400" />
            </div>
          </div>
        );

      case 'pcb-spinner':
        return (
          <div className={`pcb-spinner-loader ${sizeClasses[size]}`}>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <Cpu className="h-4 w-4 text-blue-500" />
            </div>
          </div>
        );

      case 'voltage-meter':
        return (
          <div className={`voltage-meter-loader ${sizeClasses[size]}`}>
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
              <Gauge className="h-4 w-4 text-gray-600" />
            </div>
          </div>
        );

      case 'data-flow':
        return (
          <div className={`data-flow-loader ${sizeClasses[size]}`}>
            <div className="absolute left-1 top-1/2 transform -translate-y-1/2">
              <Zap className="h-3 w-3 text-blue-500" />
            </div>
            <div className="absolute right-1 top-1/2 transform -translate-y-1/2">
              <Zap className="h-3 w-3 text-blue-500" />
            </div>
          </div>
        );

      case 'connection-points':
        return (
          <div className={`connection-points-loader ${sizeClasses[size]}`}>
            <div className="point"></div>
            <div className="point"></div>
            <div className="point"></div>
            <div className="point"></div>
          </div>
        );

      case 'pcb-test-progress':
        return (
          <div className={`pcb-test-progress-loader ${sizeClasses[size]}`}>
            <div className="components">
              <div className="component"></div>
              <div className="component"></div>
              <div className="component"></div>
            </div>
            <div className="absolute top-1 left-1">
              <CircuitBoard className="h-3 w-3 text-gray-500" />
            </div>
            <div className="absolute bottom-1 right-1">
              <Battery className="h-3 w-3 text-gray-500" />
            </div>
          </div>
        );

      default:
        return (
          <div className={`pcb-circuit-loader ${sizeClasses[size]}`}>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <CircuitBoard className="h-6 w-6 text-blue-400" />
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center gap-3 ${className}`}>
      {renderLoader()}
      {message && (
        <div className="text-center">
          <p className="text-sm font-medium text-gray-700 animate-pulse-soft">
            {message}
          </p>
        </div>
      )}
    </div>
  );
}

// Preset loader configurations for common PCB testing scenarios
export const PCBLoaderPresets = {
  testStarting: {
    type: 'pcb-test-progress' as LoaderType,
    message: 'Initializing PCB test sequence...',
  },
  charging: {
    type: 'battery-charging' as LoaderType,
    message: 'Charging battery...',
  },
  discharging: {
    type: 'electronic-wave' as LoaderType,
    message: 'Discharging battery...',
  },
  dataProcessing: {
    type: 'data-flow' as LoaderType,
    message: 'Processing test data...',
  },
  connecting: {
    type: 'connection-points' as LoaderType,
    message: 'Establishing PCB connection...',
  },
  measuring: {
    type: 'voltage-meter' as LoaderType,
    message: 'Measuring voltage levels...',
  },
  circuitAnalysis: {
    type: 'pcb-circuit' as LoaderType,
    message: 'Analyzing circuit board...',
  },
  systemCheck: {
    type: 'pcb-spinner' as LoaderType,
    message: 'Running system diagnostics...',
  },
};

// Quick access component for common scenarios
interface QuickLoaderProps {
  scenario: keyof typeof PCBLoaderPresets;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  customMessage?: string;
}

export function QuickPCBLoader({ 
  scenario, 
  size = 'md', 
  className = '', 
  customMessage 
}: QuickLoaderProps) {
  const preset = PCBLoaderPresets[scenario];
  
  return (
    <ElectronicsLoader
      type={preset.type}
      size={size}
      message={customMessage || preset.message}
      className={className}
    />
  );
}

// Loading overlay component for full-screen loading
interface LoadingOverlayProps {
  isVisible: boolean;
  scenario: keyof typeof PCBLoaderPresets;
  message?: string;
  backdrop?: boolean;
}

export function PCBLoadingOverlay({ 
  isVisible, 
  scenario, 
  message, 
  backdrop = true 
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${
      backdrop ? 'bg-black/20 backdrop-blur-sm' : ''
    }`}>
      <div className="bg-white rounded-lg p-8 shadow-xl border border-gray-200 max-w-sm w-full mx-4">
        <QuickPCBLoader
          scenario={scenario}
          size="lg"
          customMessage={message}
          className="py-4"
        />
      </div>
    </div>
  );
}
