'use client';

import { CircuitBoard, FileText, LayoutGrid, Timer, Activity } from 'lucide-react';
import { StatCard, ResultsStatCard } from './stat-card';
import { SSEStatus } from './sse-status';
import { TestStats } from '@/lib/types';
import { formatTestDuration } from '@/lib/test-utils';

interface DashboardHeaderProps {
  title: string;
  description: string;
  testStats: TestStats;
  className?: string;
}

export function DashboardHeader({
  title,
  description,
  testStats,
  className = '',
}: DashboardHeaderProps) {
  return (
    <div
      className={`mb-5 flex flex-col md:flex-row justify-between items-center gap-4 ${className}`}
    >
      {/* Dashboard title and MQTT status */}
      <div className="w-full md:w-auto bg-white shadow-md rounded-lg p-3 border border-slate-200">
        <div className="flex items-center gap-2 mb-1.5">
          <div className="bg-gradient-to-r from-blue-600 to-cyan-600 p-1.5 rounded-md">
            <Activity className="h-4 w-4 text-white" />
          </div>
          <h1 className="text-lg font-bold text-slate-800">{title}</h1>
          <div className="ml-auto">
            <SSEStatus />
          </div>
        </div>
        <p className="text-slate-600 text-sm max-w-xl font-medium">{description}</p>
      </div>

      {/* Test statistics with improved styling */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2.5 w-full md:w-auto">
        {/* Test Duration */}
        <StatCard
          icon={Timer}
          label="Duration"
          value={formatTestDuration(testStats.duration)}
          iconColor="text-blue-600"
        />

        {/* Panels Tested */}
        <StatCard
          icon={LayoutGrid}
          label="Panels"
          value={testStats.panelsTested}
          iconColor="text-indigo-600"
        />

        {/* PCBs Tested */}
        <StatCard
          icon={CircuitBoard}
          label="PCBs"
          value={testStats.pcbsTested}
          iconColor="text-cyan-600"
        />

        {/* Success/Failure Results */}
        <ResultsStatCard
          icon={FileText}
          label="Results"
          successValue={testStats.successPcbs}
          failureValue={testStats.failurePcbs}
          iconColor="text-violet-600"
        />
      </div>
    </div>
  );
}
