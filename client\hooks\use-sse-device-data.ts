'use client';

import { STATUS, StatusType, TEST_STATUS } from '@/lib/constants';
import { createInitialDevice } from '@/lib/dashboard-utils';
import sseService from '@/lib/sse-service';
import { ChannelData, DeviceData, TestResults, TestStats } from '@/lib/types';
import { useEffect, useRef, useState } from 'react';

export function useSSEDeviceData() {
  // Device data state
  const [devices, setDevices] = useState<DeviceData[]>(
    Array.from({ length: 9 }, (_, index) => createInitialDevice(index + 1))
  );

  // Initial connection data state for delayed application
  const [initialConnectionData, setInitialConnectionData] = useState<any>(null);

  // PCB status array
  const [pcbStatusArray, setPcbStatusArray] = useState<boolean[]>([]);

  // Battery levels array - used in updateDevicesFromArrays
  const [batteryLevels, setBatteryLevels] = useState<number[]>([]);

  // Test results state
  const [testResults, setTestResults] = useState<TestResults>({});

  // Test in progress state
  const [testInProgress, setTestInProgress] = useState<boolean>(false);

  // Current state (1=charging, 2=discharging, 3=charging+discharging, 4=completed)
  const [currentState, setCurrentState] = useState<number>(0);

  // Previous state data - used to track the final status of previous states
  const [previousStateData, setPreviousStateData] = useState<{
    chargingStatus: boolean[];
    dischargingStatus: boolean[];
    chargingAndDischargingStatus: boolean[];
  }>({
    chargingStatus: [],
    dischargingStatus: [],
    chargingAndDischargingStatus: [],
  });

  // Serial number state
  const [serialNumber, setSerialNumber] = useState<string>('');

  // Test stats state
  const [testStatsBase, setTestStatsBase] = useState<Omit<TestStats, 'duration'>>({
    panelsTested: 0,
    pcbsTested: 0,
    successPcbs: 0,
    failurePcbs: 0,
  });

  // Duration state
  const [duration, setDuration] = useState<number>(0);

  // Ref to track whether the counter is running
  const isRunningRef = useRef<boolean>(false);

  // Function to update the duration from external sources (like SSE)
  const updateDuration = (newDuration: number) => {
    setDuration(newDuration);
  };

  // Effect to start/stop the counter based on test status
  useEffect(() => {
    isRunningRef.current = testInProgress;
  }, [testInProgress]);

  // Effect to increment the duration counter
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (isRunningRef.current) {
        setDuration(prevDuration => prevDuration + 1);
      }
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  // Effect to update device status when PCB status changes
  // PCB status is received only once and maintained throughout the test
  useEffect(() => {
    if (pcbStatusArray.length > 0) {
      console.log('PCB status array updated:', pcbStatusArray);
      console.log('Updating device status based on new PCB status array');

      // Update device status based on PCB status
      setDevices(prevDevices => {
        console.log(
          'Previous devices before PCB status update:',
          prevDevices.map(d => ({ id: d.id, testStatus: d.testStatus }))
        );

        const updatedDevices = prevDevices.map((device, idx) => {
          const hasPCB = pcbStatusArray[idx] === true;
          console.log(
            `Device ${idx + 1}: PCB status = ${hasPCB}, previous testStatus = ${device.testStatus}`
          );

          if (hasPCB) {
            // PCB is present - preserve all existing device data
            // PCB status only determines PCB presence, not test status
            // Test status should be determined by message order, not testInProgress property

            return {
              ...device,
              // Only update battery level if available, preserve everything else
              batteryLevel: batteryLevels[idx] > 0 ? batteryLevels[idx] : device.batteryLevel || 0,
              // Preserve all existing values - PCB status change should not affect them
              // Test status, voltage, current, and status values are managed by other message handlers
            };
          } else {
            // PCB is not present - mark as NO_PCB with static dummy values
            return {
              ...device,
              testStatus: TEST_STATUS.NO_PCB,
              chargingStatusMessage: 'No PCB',
              dischargingStatusMessage: 'No PCB',
              chargingAndDischargingStatusMessage: 'No PCB',
              // Preserve battery level if available from bp array, otherwise use 0
              batteryLevel: batteryLevels[idx] > 0 ? batteryLevels[idx] : 0,
              progress: 0,
              timeRemaining: 0,
              chargingVoltage: 0,
              chargingCurrent: 0,
              dischargingVoltage: 0,
              dischargingCurrent: 0,
              isCharging: false,
              isDischarging: false,
              isChargingAndDischarging: false,
              chargingStatus: STATUS.WARNING,
              dischargingStatus: STATUS.WARNING,
              chargingAndDischargingStatus: STATUS.WARNING,
            };
          }
        });

        console.log(
          'Devices after PCB status update:',
          updatedDevices.map(d => ({
            id: d.id,
            testStatus: d.testStatus,
            batteryLevel: d.batteryLevel,
          }))
        );

        return updatedDevices;
      });
    }
  }, [pcbStatusArray, batteryLevels]);

  // Effect to apply initial connection data after PCB status has been processed
  useEffect(() => {
    console.log('Initial connection data effect triggered:', {
      hasInitialData: !!initialConnectionData,
      pcbStatusArrayLength: pcbStatusArray.length,
      initialConnectionData: initialConnectionData ? 'present' : 'null',
    });

    if (initialConnectionData && pcbStatusArray.length > 0) {
      console.log(
        'Applying initial connection data after PCB status update:',
        initialConnectionData
      );

      console.log('Initial connection data details:', {
        pcb_status: initialConnectionData.pcb_status,
        bp: initialConnectionData.bp,
        currentState: initialConnectionData.currentState,
        chargingVoltage: initialConnectionData.chargingVoltage,
        chargingCurrent: initialConnectionData.chargingCurrent,
        dischargingVoltage: initialConnectionData.dischargingVoltage,
        dischargingCurrent: initialConnectionData.dischargingCurrent,
      });

      // Apply the initial connection data to devices
      setDevices(prevDevices => {
        return prevDevices.map((device, idx) => {
          // Skip devices without PCBs
          if (!initialConnectionData.pcb_status[idx]) {
            return {
              ...device,
              testStatus: TEST_STATUS.NO_PCB,
              batteryLevel: initialConnectionData.bp[idx] || device.batteryLevel || 0,
            };
          }

          // For devices with PCBs, update with initial connection data
          // Test status is determined by presence of data, not testInProgress property
          const updatedDevice = {
            ...device,
            testStatus: TEST_STATUS.IN_PROGRESS, // If we have initial data, test is in progress
            batteryLevel: initialConnectionData.bp[idx] || device.batteryLevel || 0,

            // Set active state flags based on current state
            isCharging: initialConnectionData.currentState === 1,
            isDischarging: initialConnectionData.currentState === 2,
            isChargingAndDischarging: initialConnectionData.currentState === 3,

            // Set hasState3Occurred if we have state 3 data or are in state 3
            hasState3Occurred:
              initialConnectionData.currentState >= 3 ||
              (initialConnectionData.chargingAndDischargingStatus &&
                initialConnectionData.chargingAndDischargingStatus.some(
                  (status: any) => status !== undefined
                )),

            // Set voltage and current values directly from initial data
            chargingVoltage:
              initialConnectionData.chargingVoltage?.[idx] !== undefined
                ? initialConnectionData.chargingVoltage[idx]
                : device.chargingVoltage || 0,
            chargingCurrent:
              initialConnectionData.chargingCurrent?.[idx] !== undefined
                ? initialConnectionData.chargingCurrent[idx]
                : device.chargingCurrent || 0,
            dischargingVoltage:
              initialConnectionData.dischargingVoltage?.[idx] !== undefined
                ? initialConnectionData.dischargingVoltage[idx]
                : device.dischargingVoltage || 0,
            dischargingCurrent:
              initialConnectionData.dischargingCurrent?.[idx] !== undefined
                ? initialConnectionData.dischargingCurrent[idx]
                : device.dischargingCurrent || 0,

            // Set status values based on current state and available data
            chargingStatus:
              initialConnectionData.chargingStatus?.[idx] === true
                ? STATUS.OK
                : initialConnectionData.chargingStatus?.[idx] === false
                ? STATUS.ERROR
                : device.chargingStatus || STATUS.WARNING,

            dischargingStatus:
              initialConnectionData.currentState === 2
                ? STATUS.WARNING
                : initialConnectionData.dischargingStatus?.[idx] === true
                ? STATUS.OK
                : initialConnectionData.dischargingStatus?.[idx] === false
                ? STATUS.ERROR
                : device.dischargingStatus || STATUS.WARNING,

            chargingAndDischargingStatus:
              initialConnectionData.currentState === 3
                ? STATUS.WARNING
                : initialConnectionData.chargingAndDischargingStatus?.[idx] === true
                ? STATUS.OK
                : initialConnectionData.chargingAndDischargingStatus?.[idx] === false
                ? STATUS.ERROR
                : device.chargingAndDischargingStatus || STATUS.WARNING,

            // Set status messages based on current state and available data
            chargingStatusMessage:
              initialConnectionData.chargingStatus?.[idx] === true
                ? 'Success'
                : initialConnectionData.chargingStatus?.[idx] === false
                ? 'Failed'
                : 'In Progress',

            dischargingStatusMessage:
              initialConnectionData.currentState === 2
                ? 'In Progress'
                : initialConnectionData.dischargingStatus?.[idx] === true
                ? 'Success'
                : initialConnectionData.dischargingStatus?.[idx] === false
                ? 'Failed'
                : 'Not Started',

            chargingAndDischargingStatusMessage:
              initialConnectionData.currentState === 3
                ? 'In Progress'
                : initialConnectionData.chargingAndDischargingStatus?.[idx] === true
                ? 'Success'
                : initialConnectionData.chargingAndDischargingStatus?.[idx] === false
                ? 'Failed'
                : 'Not Started',

            // Set progress and time remaining
            progress:
              initialConnectionData.progress?.[idx] !== undefined
                ? initialConnectionData.progress[idx]
                : device.progress || 0,
            timeRemaining:
              initialConnectionData.timeRemaining?.[idx] !== undefined
                ? initialConnectionData.timeRemaining[idx]
                : device.timeRemaining || 0,
          };

          // Log the device data after applying initial connection data
          console.log(`Device ${idx + 1} after applying initial connection data:`, {
            testStatus: updatedDevice.testStatus,
            batteryLevel: updatedDevice.batteryLevel,
            currentState: initialConnectionData.currentState,
            isCharging: updatedDevice.isCharging,
            isDischarging: updatedDevice.isDischarging,
            isChargingAndDischarging: updatedDevice.isChargingAndDischarging,
            chargingVoltage: updatedDevice.chargingVoltage,
            chargingCurrent: updatedDevice.chargingCurrent,
            dischargingVoltage: updatedDevice.dischargingVoltage,
            dischargingCurrent: updatedDevice.dischargingCurrent,
            chargingStatus: updatedDevice.chargingStatus,
            dischargingStatus: updatedDevice.dischargingStatus,
            chargingStatusMessage: updatedDevice.chargingStatusMessage,
            dischargingStatusMessage: updatedDevice.dischargingStatusMessage,
          });

          return updatedDevice;
        });
      });

      // Clear the initial connection data after applying it
      setInitialConnectionData(null);
      console.log('Initial connection data applied and cleared');
    }
  }, [initialConnectionData, pcbStatusArray]);

  // Combine base test stats with duration into complete test stats
  const testStats: TestStats = {
    ...testStatsBase,
    duration,
  };

  // Removed completionTrigger as it's not needed

  // Connect to SSE service
  useEffect(() => {
    sseService.connect();

    // Register message handler
    sseService.onMessage(handleSSEMessage);

    // Cleanup on unmount
    return () => {
      sseService.removeMessageHandler(handleSSEMessage);
    };
  }, []);

  // Handle SSE messages
  const handleSSEMessage = (data: any) => {
    try {
      console.log('SSE message in hook:', data);

      // Handle test stats update
      if (data.testStats) {
        try {
          // Update duration from SSE data
          if (data.testStats.duration !== undefined) {
            updateDuration(data.testStats.duration);
          }

          // Update other test stats
          setTestStatsBase({
            panelsTested: data.testStats.panelsTested || 0,
            pcbsTested: data.testStats.pcbsTested || 0,
            successPcbs: data.testStats.successPcbs || 0,
            failurePcbs: data.testStats.failurePcbs || 0,
          });
        } catch (error) {
          console.error('Error updating test stats:', error);
        }
      }
    } catch (error) {
      console.error('Error in handleSSEMessage:', error);
      return; // Exit early if there's a critical error
    }

    // Handle test state changes based on message content
    // If we receive PCB status with device data, test is starting/in progress
    if (
      data.pcb_status &&
      (data.chargingStatus || data.dischargingStatus || data.chargingAndDischargingStatus)
    ) {
      if (!testInProgress) {
        console.log('Test starting - received PCB status with device data');
        setTestInProgress(true);
      }
    }
    // If we only receive testStats without PCB status, test has completed
    else if (data.testStats && !data.pcb_status && testInProgress) {
      console.log('Test completed - received testStats without PCB status');
      setTestInProgress(false);
    }

    // Handle PCB status and battery levels
    if (data.pcb_status) {
      console.log('Received PCB status:', data.pcb_status);
      console.log('Previous PCB status array:', pcbStatusArray);

      // Clear old test data when new PCB status is received (indicates new test starting)
      console.log('New PCB status received - clearing old test data and resetting devices');
      setTestResults({});
      setCurrentState(0);
      setPreviousStateData({
        chargingStatus: [],
        dischargingStatus: [],
        chargingAndDischargingStatus: [],
      });

      // Reset all devices to initial state before applying new PCB status
      setDevices(Array.from({ length: 9 }, (_, index) => createInitialDevice(index + 1)));

      // Completely replace the old PCB status array with the new one
      setPcbStatusArray(data.pcb_status);
      console.log('PCB status array updated to:', data.pcb_status);
    }

    // Handle battery levels - sent on initial connection and during tests
    if (data.bp && Array.isArray(data.bp)) {
      console.log('Received battery levels:', data.bp);
      setBatteryLevels(data.bp);

      // Check if this is initial connection data with device status arrays (mid-test connection)
      // OR if this is a new test start with PCB status, device data, bp, and testStats together
      const isInitialConnectionDataWithDeviceStatus =
        data.pcb_status &&
        (data.chargingStatus || data.dischargingStatus || data.chargingAndDischargingStatus);

      const isNewTestStartWithCompleteData =
        data.pcb_status &&
        data.bp &&
        data.testStats &&
        (data.chargingStatus || data.dischargingStatus || data.chargingAndDischargingStatus);

      if (!isInitialConnectionDataWithDeviceStatus && !isNewTestStartWithCompleteData) {
        console.log('Updating battery levels immediately (initial connection or test start)');
        // Update device battery levels directly - include 0 values as they are valid battery levels
        setDevices(prevDevices => {
          return prevDevices.map((device, idx) => {
            if (idx < data.bp.length) {
              console.log(`Setting battery level for device ${idx + 1} to ${data.bp[idx]}%`);
              return {
                ...device,
                batteryLevel: data.bp[idx],
              };
            }
            return device;
          });
        });
      } else {
        console.log(
          'Skipping immediate battery level update - will be handled by delayed application for mid-test connection or new test with complete data'
        );
      }
    }

    // Handle serial number
    if (data.serialNumber) {
      try {
        setSerialNumber(data.serialNumber);
      } catch (error) {
        console.error('Error setting serial number:', error);
      }
    }

    // Store status data for later use (don't determine state from this)
    if (data.chargingStatus && data.dischargingStatus && data.chargingAndDischargingStatus) {
      try {
        console.log('Storing status data from SSE message');
        setPreviousStateData({
          chargingStatus: data.chargingStatus,
          dischargingStatus: data.dischargingStatus,
          chargingAndDischargingStatus: data.chargingAndDischargingStatus,
        });
      } catch (error) {
        console.error('Error storing previous state data:', error);
      }
    }

    // Handle direct duration update (if not part of testStats)
    if (data.duration !== undefined && !data.testStats) {
      try {
        updateDuration(data.duration);
      } catch (error) {
        console.error('Error updating duration:', error);
      }
    }

    // Handle state change - state 4 means test completion
    if (data.state !== undefined) {
      try {
        const newState = data.state;
        console.log('State change received:', newState);

        // Update current state for reference
        setCurrentState(newState);

        // Handle test completion (state 4)
        if (newState === 4) {
          console.log('Test completed - state 4 received');
          setTestInProgress(false);

          // Mark all devices as completed and finalize their status
          setDevices(prevDevices => {
            const updatedDevices = [...prevDevices];

            // Update each device's state
            updatedDevices.forEach((device, index) => {
              // Skip devices without PCBs - they should remain unchanged with static dummy values
            if (device.testStatus === TEST_STATUS.NO_PCB) {
              return;
            }

            // Always update to IN_PROGRESS when a state change occurs
            updatedDevices[index].testStatus = TEST_STATUS.IN_PROGRESS;

            // Set active states directly on the device based on new state
            if (newState === 1) {
              updatedDevices[index].isCharging = true;
              updatedDevices[index].isDischarging = false;
              updatedDevices[index].isChargingAndDischarging = false;
            } else if (newState === 2) {
              updatedDevices[index].isCharging = false;
              updatedDevices[index].isDischarging = true;
              updatedDevices[index].isChargingAndDischarging = false;
            } else if (newState === 3) {
              updatedDevices[index].isCharging = false;
              updatedDevices[index].isDischarging = false;
              updatedDevices[index].isChargingAndDischarging = true;
              updatedDevices[index].hasState3Occurred = true;
            }

            // Update status messages based on state transitions
            if (newState === 1) {
              // Starting charging state
              updatedDevices[index] = {
                ...device, // Preserve all existing values
                isCharging: true,
                isDischarging: false,
                isChargingAndDischarging: false,
                chargingStatusMessage: 'In Progress',
                chargingStatus: STATUS.WARNING,
                // Preserve battery level
                batteryLevel: device.batteryLevel || 0,
                // Preserve voltage and current values - don't reset them
              };
            } else if (newState === 2) {
              // Moving to discharging state
              updatedDevices[index] = {
                ...device, // Preserve all existing values including charging values
                isCharging: false,
                isDischarging: true,
                isChargingAndDischarging: false,
                // Mark charging as completed
                chargingStatusMessage: 'Completed',
                dischargingStatusMessage: 'In Progress',
                dischargingStatus: STATUS.WARNING,
                // Preserve battery level
                batteryLevel: device.batteryLevel || 0,
                // Preserve charging voltage and current values - don't reset them
              };
            } else if (newState === 3) {
              // Moving to charging+discharging state
              updatedDevices[index] = {
                ...device, // Preserve all existing values including charging and discharging values
                isCharging: false,
                isDischarging: false,
                isChargingAndDischarging: true,
                hasState3Occurred: true,
                // Mark previous states as completed
                chargingStatusMessage: 'Completed',
                dischargingStatusMessage: 'Completed',
                chargingAndDischargingStatusMessage: 'In Progress',
                chargingAndDischargingStatus: STATUS.WARNING,
                // Preserve battery level
                batteryLevel: device.batteryLevel || 0,
                // Preserve charging and discharging voltage and current values - don't reset them
              };
            } else if (newState === 4) {
              // Test completed - determine success/failure from device status values
              console.log(`State 4 (test completion) received for device ${device.id}`);

              // Determine if the device passed all tests based on current status values
              // Use the status values directly from the device, which are updated during each state
              const chargingSuccess =
                typeof device.chargingStatus === 'boolean'
                  ? device.chargingStatus
                  : device.chargingStatus === STATUS.OK;

              const dischargingSuccess =
                typeof device.dischargingStatus === 'boolean'
                  ? device.dischargingStatus
                  : device.dischargingStatus === STATUS.OK;

              const combinedSuccess =
                typeof device.chargingAndDischargingStatus === 'boolean'
                  ? device.chargingAndDischargingStatus
                  : device.chargingAndDischargingStatus === STATUS.OK;

              // A device is successful if it passed all the states that occurred
              // For devices without state 3, only check charging and discharging
              const isSuccess = device.hasState3Occurred
                ? chargingSuccess && dischargingSuccess && combinedSuccess
                : chargingSuccess && dischargingSuccess;

              console.log(`Device ${device.id} test completion evaluation:`, {
                chargingStatus: device.chargingStatus,
                dischargingStatus: device.dischargingStatus,
                chargingAndDischargingStatus: device.chargingAndDischargingStatus,
                chargingSuccess,
                dischargingSuccess,
                combinedSuccess,
                hasState3Occurred: device.hasState3Occurred,
                isSuccess,
              });

              // Update test status
              updatedDevices[index] = {
                ...device, // Preserve all existing values including charging and discharging values
                isCharging: false,
                isDischarging: false,
                isChargingAndDischarging: false,
                testStatus: isSuccess ? TEST_STATUS.COMPLETED : TEST_STATUS.FAILED,
                // Update status messages based on actual status values
                chargingStatusMessage: chargingSuccess ? 'Success' : 'Failed',
                dischargingStatusMessage: dischargingSuccess ? 'Success' : 'Failed',
                chargingAndDischargingStatusMessage: device.hasState3Occurred
                  ? combinedSuccess
                    ? 'Success'
                    : 'Failed'
                  : 'Not Tested',
                // Update status indicators - use boolean values directly
                chargingStatus: chargingSuccess,
                dischargingStatus: dischargingSuccess,
                chargingAndDischargingStatus: device.hasState3Occurred ? combinedSuccess : false,
                // Preserve battery level
                batteryLevel: device.batteryLevel || 0,
                // Preserve all voltage and current values - don't reset them
                chargingVoltage: device.chargingVoltage || 0,
                chargingCurrent: device.chargingCurrent || 0,
                dischargingVoltage: device.dischargingVoltage || 0,
                dischargingCurrent: device.dischargingCurrent || 0,
              };
            }
          });

          return updatedDevices;
        });
      } catch (error) {
        console.error('Error in state change handling:', error);
      }
    }

    // Handle PCB status - indicates NEW TEST START
    // PCB status received = new test is starting
    // States 1-3 can be received in any order through channel data
    // State 4 received = test completed/finished
    if (data.pcb_status && data.bp) {
      try {
        console.log('PCB status received - NEW TEST STARTING:', data);

        // Set test as in progress
        setTestInProgress(true);

        // Reset current state since test is starting fresh
        setCurrentState(0);

        // Clear any previous state data since this is a new test
        setPreviousStateData({
          chargingStatus: [],
          dischargingStatus: [],
          chargingAndDischargingStatus: [],
        });

      // Add detailed logging for charging/discharging values
      if (data.chargingVoltage && Array.isArray(data.chargingVoltage)) {
        console.log('Initial charging voltage values:', data.chargingVoltage);
      }
      if (data.chargingCurrent && Array.isArray(data.chargingCurrent)) {
        console.log('Initial charging current values:', data.chargingCurrent);
      }
      if (data.dischargingVoltage && Array.isArray(data.dischargingVoltage)) {
        console.log('Initial discharging voltage values:', data.dischargingVoltage);
      }
      if (data.dischargingCurrent && Array.isArray(data.dischargingCurrent)) {
        console.log('Initial discharging current values:', data.dischargingCurrent);
      }

      // Store PCB status array and battery levels FIRST before updating devices
      // This ensures the PCB status effect doesn't override the initial device data
      if (Array.isArray(data.pcb_status)) {
        console.log('Setting PCB status array from initial connection:', data.pcb_status);
        setPcbStatusArray(data.pcb_status);
      }

      if (Array.isArray(data.bp) && data.bp.some((level: number) => level > 0)) {
        console.log('Setting battery levels array from initial connection:', data.bp);
        setBatteryLevels(data.bp);
      }

      // Determine the current state based on which state-specific properties are present
      let inferredState = 1; // Default to charging state

      // If we have discharging data, we're at least in state 2
      // Check for both the presence of the properties and their values
      if (
        (data.dischargingStatus &&
          Array.isArray(data.dischargingStatus) &&
          data.dischargingStatus.some(Boolean)) ||
        (data.dischargingVoltage &&
          Array.isArray(data.dischargingVoltage) &&
          data.dischargingVoltage.some((v: number) => v > 0)) ||
        (data.dischargingCurrent &&
          Array.isArray(data.dischargingCurrent) &&
          data.dischargingCurrent.some((c: number) => c > 0))
      ) {
        inferredState = 2;
        console.log('Inferred state 2 (discharging) from initial connection data');
      }

      // If we have charging and discharging data, we're in state 3
      if (
        data.chargingAndDischargingStatus &&
        Array.isArray(data.chargingAndDischargingStatus) &&
        data.chargingAndDischargingStatus.some(Boolean)
      ) {
        inferredState = 3;
        console.log('Inferred state 3 (charging+discharging) from initial connection data');
      }

      console.log('Inferred current state from initial connection:', inferredState);
      setCurrentState(inferredState);

      // If we're receiving initial data with a state > 1, store the previous state data
      if (inferredState > 1 && data.chargingStatus) {
        console.log(
          'Storing previous state data from initial connection for inferred state:',
          inferredState
        );

        // Store previous state data
        setPreviousStateData({
          chargingStatus: data.chargingStatus || [],
          dischargingStatus: data.dischargingStatus || [],
          chargingAndDischargingStatus: data.chargingAndDischargingStatus || [],
        });

        // Also update test results based on the previous state data
        const newTestResults = { ...testResults };

        // For each device with a PCB
        data.pcb_status.forEach((hasPCB: boolean, idx: number) => {
          if (hasPCB) {
            const deviceId = idx + 1;

            // Initialize test results for this device if not already present
            if (!newTestResults[deviceId]) {
              newTestResults[deviceId] = {};
            }

            // If we're in state 2 or higher, set charging success from previous state
            if (inferredState >= 2 && data.chargingStatus) {
              newTestResults[deviceId].chargingSuccess = data.chargingStatus[idx];
              console.log(
                `Setting initial charging success for device ${deviceId}:`,
                data.chargingStatus[idx]
              );
            }

            // If we're in state 3 or higher, set discharging success from previous state
            if (inferredState >= 3 && data.dischargingStatus) {
              newTestResults[deviceId].dischargingSuccess = data.dischargingStatus[idx];
              console.log(
                `Setting initial discharging success for device ${deviceId}:`,
                data.dischargingStatus[idx]
              );
            }
          }
        });

        // Update test results state
        setTestResults(newTestResults);
        console.log('Updated test results from initial connection:', newTestResults);
      }

      // Log the actual values being passed to direct device update
      console.log('Initial connection data for direct device update:', {
        chargingVoltage: data.chargingVoltage || [],
        chargingCurrent: data.chargingCurrent || [],
        dischargingVoltage: data.dischargingVoltage || [],
        dischargingCurrent: data.dischargingCurrent || [],
        chargingStatus: data.chargingStatus || [],
        dischargingStatus: data.dischargingStatus || [],
      });

      // Store the initial connection data for later use in a separate effect
      // This ensures the data is applied after PCB status effect has run
      setInitialConnectionData({
        pcb_status: data.pcb_status,
        bp: data.bp,
        chargingVoltage: data.chargingVoltage || [],
        chargingCurrent: data.chargingCurrent || [],
        dischargingVoltage: data.dischargingVoltage || [],
        dischargingCurrent: data.dischargingCurrent || [],
        chargingStatus: data.chargingStatus || [],
        dischargingStatus: data.dischargingStatus || [],
        chargingAndDischargingStatus: data.chargingAndDischargingStatus || [],
        progress: data.progress || [],
        timeRemaining: data.timeRemaining || [],
        currentState: inferredState,
      });

      console.log('Initial connection data stored for delayed application');
      } catch (error) {
        console.error('Error handling PCB status (new test start):', error);
      }
    }

    // Handle channel data updates (array of channel objects)
    if (Array.isArray(data) && data.length > 0 && typeof data[0].channel === 'number') {
      // Channel data is sent directly without battery levels
      const channelData = data;

      console.log('Received channel data:', channelData);

      // Check if this is the new format with charging/discharging data structure
      if (
        (channelData[0].discharging && typeof channelData[0].discharging === 'object') ||
        (channelData[0].charging && typeof channelData[0].charging === 'object')
      ) {
        console.log('Detected new channel data format with charging/discharging objects');
        // Use the new format handler
        processNewChannelDataFormat(channelData as ChannelData[]);
      } else {
        console.log('Using original channel data handler');
        // Use the original handler for backward compatibility
        handleChannelDataMessage(channelData as ChannelData[]);
      }
    }
  };

  // Process channel data with the new format
  const processNewChannelDataFormat = (channelsData: ChannelData[]) => {
    console.log('Processing new format channel data:', channelsData);

    // Process channel data
    setDevices(prevDevices => {
      const updatedDevices = [...prevDevices];

      // Process each channel
      for (const channelData of channelsData) {
        const channelIndex = channelData.channel;

        console.log(`Processing channel ${channelIndex} data:`, channelData);

        // Calculate base index for this channel (3 devices per channel)
        const baseIndex = (channelIndex - 1) * 3;

        // Process each device in the channel
        for (let i = 0; i < Math.min(3, channelData.progress.length); i++) {
          const deviceArrayIndex = baseIndex + i;
          const deviceId = deviceArrayIndex + 1; // 1-based device ID

          // Skip if device index is out of bounds
          if (deviceArrayIndex >= updatedDevices.length) {
            console.warn(`Device index ${deviceArrayIndex} out of bounds`);
            continue;
          }

          // Get the previous device state
          const prevDevice = updatedDevices[deviceArrayIndex];

          // Check if PCB is present for this device - use the existing testStatus
          const hasPCB = prevDevice.testStatus !== TEST_STATUS.NO_PCB;

          console.log(`Device ${deviceId} PCB status:`, hasPCB);

          // Skip devices without PCBs - they should remain unchanged with static dummy values
          if (!hasPCB) {
            console.log(`Skipping device ${deviceId} - no PCB`);
            continue;
          }

          // Extract data for this device
          const progress = channelData.progress[i];
          const elapsedTime = channelData.elapsed_time[i];
          const status = channelData.status?.[i];

          // Get discharging data - preserve previous values if not present in current data
          // This ensures values are not reset when switching between states
          const dischargingVoltage =
            channelData.discharging?.V?.[i] !== undefined
              ? channelData.discharging.V[i]
              : prevDevice.dischargingVoltage || 0;
          const dischargingCurrent =
            channelData.discharging?.C?.[i] !== undefined
              ? channelData.discharging.C[i]
              : prevDevice.dischargingCurrent || 0;

          // Get charging data - preserve previous values if not present in current data
          // This ensures values are not reset when switching between states
          const chargingVoltage =
            channelData.charging?.V?.[i] !== undefined
              ? channelData.charging.V[i]
              : prevDevice.chargingVoltage || 0;
          const chargingCurrent =
            channelData.charging?.C?.[i] !== undefined
              ? channelData.charging.C[i]
              : prevDevice.chargingCurrent || 0;

          console.log(`Preserved values for device ${deviceId}:`, {
            chargingVoltage,
            chargingCurrent,
            dischargingVoltage,
            dischargingCurrent,
            prevChargingVoltage: prevDevice.chargingVoltage,
            prevChargingCurrent: prevDevice.chargingCurrent,
            prevDischargingVoltage: prevDevice.dischargingVoltage,
            prevDischargingCurrent: prevDevice.dischargingCurrent,
          });

          // Log the extracted data for debugging
          console.log(`Channel data for device ${deviceId}:`, {
            progress,
            elapsedTime,
            status,
            dischargingVoltage,
            dischargingCurrent,
            chargingVoltage,
            chargingCurrent,
          });

          // Check for the presence of properties in the channel data, not the values
          // This ensures we determine the state based on which properties are present
          const hasChargingData = channelData.charging !== undefined;
          const hasDischargingData = channelData.discharging !== undefined;

          console.log(`State determination for device ${deviceId}:`, {
            hasChargingDataInChannel: hasChargingData,
            hasDischargingDataInChannel: hasDischargingData,
            hasChargingProperty: channelData.charging !== undefined,
            hasDischargingProperty: channelData.discharging !== undefined,
            channelDataKeys: Object.keys(channelData),
            channelDataStructure: JSON.stringify(channelData).substring(0, 100) + '...',
          });

          console.log(`Device ${deviceId} data detection:`, {
            hasChargingData,
            hasDischargingData,
            chargingVoltage,
            chargingCurrent,
            dischargingVoltage,
            dischargingCurrent,
          });

          // Determine device state based on channel data only (not currentState)
          // This ensures we always show the correct current state regardless of state transitions
          let isCharging = false;
          let isDischarging = false;
          let isChargingAndDischarging = false;

          // Determine state based on which properties are present in channel data
          // This is the primary method for state determination
          if (channelData.charging && !channelData.discharging) {
            isCharging = true;
            console.log(
              `Device ${deviceId} is currently charging (channel data has charging only)`
            );
          } else if (!channelData.charging && channelData.discharging) {
            isDischarging = true;
            console.log(
              `Device ${deviceId} is currently discharging (channel data has discharging only)`
            );
          } else if (channelData.charging && channelData.discharging) {
            isChargingAndDischarging = true;
            console.log(
              `Device ${deviceId} is currently charging and discharging (channel data has both)`
            );
          } else {
            // No active state detected from channel data
            console.log(`Device ${deviceId} has no active state in channel data`);
          }

          console.log(`Device ${deviceId} state determination:`, {
            currentState,
            isCharging,
            isDischarging,
            isChargingAndDischarging,
            hasPreviousStateData: previousStateData.chargingStatus.length > 0,
          });

          // Create a new device object with updated values
          const newDeviceData: DeviceData = {
            ...prevDevice,
            // Always set to IN_PROGRESS when channel data is received for devices with PCBs
            testStatus: TEST_STATUS.IN_PROGRESS,
            // Set states based on data
            isCharging,
            isDischarging,
            isChargingAndDischarging,
            // Update progress and time
            progress: typeof progress === 'number' ? progress : prevDevice.progress,
            timeRemaining: typeof elapsedTime === 'number' ? elapsedTime : prevDevice.timeRemaining,
            // Update voltage and current values
            dischargingVoltage,
            dischargingCurrent,
            chargingVoltage,
            chargingCurrent,
            // Use the battery level from the array if it's available and greater than 0
            // Otherwise preserve the existing battery level
            batteryLevel:
              batteryLevels[deviceArrayIndex] > 0
                ? batteryLevels[deviceArrayIndex]
                : prevDevice.batteryLevel || 0,
          };

          // Apply previous state data if we're in state 2 or higher
          if (currentState >= 2 && previousStateData.chargingStatus.length > 0) {
            const prevChargingStatus = previousStateData.chargingStatus[deviceArrayIndex];

            // Update charging status based on previous state data
            newDeviceData.chargingStatus = prevChargingStatus
              ? (STATUS.OK as StatusType)
              : (STATUS.ERROR as StatusType);
            newDeviceData.chargingStatusMessage = prevChargingStatus ? 'Success' : 'Failed';

            // Update test results for charging
            setTestResults(prev => ({
              ...prev,
              [deviceId]: {
                ...prev[deviceId],
                chargingSuccess: prevChargingStatus,
              },
            }));

            console.log(`Applied previous charging status for device ${deviceId}:`, {
              prevChargingStatus,
              status: newDeviceData.chargingStatus,
              message: newDeviceData.chargingStatusMessage,
            });
          }

          // Apply previous state data if we're in state 3 or higher
          if (currentState >= 3 && previousStateData.dischargingStatus.length > 0) {
            const prevDischargingStatus = previousStateData.dischargingStatus[deviceArrayIndex];

            // Update discharging status based on previous state data
            newDeviceData.dischargingStatus = prevDischargingStatus
              ? (STATUS.OK as StatusType)
              : (STATUS.ERROR as StatusType);
            newDeviceData.dischargingStatusMessage = prevDischargingStatus ? 'Success' : 'Failed';

            // Update test results for discharging
            setTestResults(prev => ({
              ...prev,
              [deviceId]: {
                ...prev[deviceId],
                dischargingSuccess: prevDischargingStatus,
              },
            }));

            console.log(`Applied previous discharging status for device ${deviceId}:`, {
              prevDischargingStatus,
              status: newDeviceData.dischargingStatus,
              message: newDeviceData.dischargingStatusMessage,
            });
          }

          // Set status messages and status based on active states
          // Only set status to WARNING if we don't already have a status from previous state data
          if (isCharging && !(currentState >= 2 && previousStateData.chargingStatus.length > 0)) {
            newDeviceData.chargingStatus = STATUS.WARNING as StatusType;
            newDeviceData.chargingStatusMessage = 'In Progress';
          }

          if (
            isDischarging &&
            !(currentState >= 3 && previousStateData.dischargingStatus.length > 0)
          ) {
            newDeviceData.dischargingStatus = STATUS.WARNING as StatusType;
            newDeviceData.dischargingStatusMessage = 'In Progress';
          }

          if (isChargingAndDischarging) {
            newDeviceData.chargingAndDischargingStatus = STATUS.WARNING as StatusType;
            newDeviceData.chargingAndDischargingStatusMessage = 'In Progress';
          }

          // Update status based on channel data status and current active state
          if (status !== undefined) {
            // Convert boolean status to string status
            const statusValue = status ? (STATUS.OK as StatusType) : (STATUS.ERROR as StatusType);
            const statusMessage = status ? 'Success' : 'Failed';

            // Update the appropriate status based on currently active state (not currentState)
            if (isCharging) {
              newDeviceData.chargingStatus = statusValue;
              newDeviceData.chargingStatusMessage = statusMessage;

              // Update test results
              setTestResults(prev => ({
                ...prev,
                [deviceId]: {
                  ...prev[deviceId],
                  chargingSuccess: status,
                },
              }));

              console.log(`Updated charging status for device ${deviceId}: ${statusMessage}`);
            } else if (isDischarging) {
              // Update discharging status
              newDeviceData.dischargingStatus = statusValue;
              newDeviceData.dischargingStatusMessage = statusMessage;

              // Update test results for discharging
              setTestResults(prev => ({
                ...prev,
                [deviceId]: {
                  ...prev[deviceId],
                  dischargingSuccess: status,
                },
              }));

              console.log(`Updated discharging status for device ${deviceId}: ${statusMessage}`);
            } else if (isChargingAndDischarging) {
              // Update charging and discharging status
              newDeviceData.chargingAndDischargingStatus = statusValue;
              newDeviceData.chargingAndDischargingStatusMessage = statusMessage;

              // Update test results for combined state
              setTestResults(prev => ({
                ...prev,
                [deviceId]: {
                  ...prev[deviceId],
                  chargingAndDischargingSuccess: status,
                },
              }));

              console.log(
                `Updated charging+discharging status for device ${deviceId}: ${statusMessage}`
              );
            } else if (currentState === 3) {
              // For state 3, update charging+discharging status
              newDeviceData.chargingAndDischargingStatus = statusValue;
              newDeviceData.chargingAndDischargingStatusMessage = statusMessage;

              // We already applied the previous state data earlier, so we don't need to do it again
              // Just log that we're using the current state data
              console.log(`Using current state data for device ${deviceId} in state 3`);

              // Make sure we update the test results for the current state
              setTestResults(prev => {
                const updatedResults = {
                  ...prev,
                  [deviceId]: {
                    ...prev[deviceId],
                    chargingAndDischargingSuccess: status,
                  },
                };

                // Log the updated test results
                console.log(
                  `Updated test results for device ${deviceId}:`,
                  updatedResults[deviceId]
                );

                return updatedResults;
              });
            } else {
              // If no specific state, update based on active state
              if (isCharging) {
                newDeviceData.chargingStatus = statusValue;
                newDeviceData.chargingStatusMessage = statusMessage;

                setTestResults(prev => ({
                  ...prev,
                  [deviceId]: {
                    ...prev[deviceId],
                    chargingSuccess: status,
                  },
                }));
              } else if (isDischarging) {
                newDeviceData.dischargingStatus = statusValue;
                newDeviceData.dischargingStatusMessage = statusMessage;

                setTestResults(prev => ({
                  ...prev,
                  [deviceId]: {
                    ...prev[deviceId],
                    dischargingSuccess: status,
                  },
                }));
              } else if (isChargingAndDischarging) {
                newDeviceData.chargingAndDischargingStatus = statusValue;
                newDeviceData.chargingAndDischargingStatusMessage = statusMessage;

                setTestResults(prev => ({
                  ...prev,
                  [deviceId]: {
                    ...prev[deviceId],
                    chargingAndDischargingSuccess: status,
                  },
                }));
              }
            }
          }

          // Log the updated device data for debugging
          console.log(`Updated device ${deviceId} data:`, {
            testStatus: newDeviceData.testStatus,
            currentState,
            chargingVoltage: newDeviceData.chargingVoltage,
            chargingCurrent: newDeviceData.chargingCurrent,
            dischargingVoltage: newDeviceData.dischargingVoltage,
            dischargingCurrent: newDeviceData.dischargingCurrent,
            isCharging: newDeviceData.isCharging,
            isDischarging: newDeviceData.isDischarging,
            isChargingAndDischarging: newDeviceData.isChargingAndDischarging,
            chargingStatus: newDeviceData.chargingStatus,
            chargingStatusMessage: newDeviceData.chargingStatusMessage,
            dischargingStatus: newDeviceData.dischargingStatus,
            dischargingStatusMessage: newDeviceData.dischargingStatusMessage,
          });

          // Log formatted values as they would appear in the device card
          console.log(`Device ${deviceId} card values:`, {
            chargingVoltage: `${(newDeviceData.chargingVoltage || 0).toFixed(2)}V`,
            chargingCurrent: `${(newDeviceData.chargingCurrent || 0).toFixed(3)}A`,
            dischargingVoltage: `${(newDeviceData.dischargingVoltage || 0).toFixed(2)}V`,
            dischargingCurrent: `${(newDeviceData.dischargingCurrent || 0).toFixed(3)}A`,
          });

          // Log accumulated test results for this device
          setTestResults(prev => {
            const deviceResults = prev[deviceId] || {};
            console.log(`Accumulated test results for device ${deviceId}:`, {
              chargingSuccess: deviceResults.chargingSuccess,
              dischargingSuccess: deviceResults.dischargingSuccess,
              chargingAndDischargingSuccess: deviceResults.chargingAndDischargingSuccess,
              currentState,
              overallSuccess:
                deviceResults.chargingSuccess !== false &&
                deviceResults.dischargingSuccess !== false &&
                (currentState < 3 || deviceResults.chargingAndDischargingSuccess !== false),
            });
            return prev;
          });

          // Update the device in the array
          updatedDevices[deviceArrayIndex] = newDeviceData;
        }
      }

      return updatedDevices;
    });
  };

  // Handle channel data message
  const handleChannelDataMessage = (channelsData: ChannelData[]) => {
    // Add debug log to help diagnose issues
    console.log('Processing channel data:', {
      channelsCount: channelsData.length,
      testInProgress,
      firstChannelSample: channelsData[0],
      pcbStatus: pcbStatusArray,
    });

    // Process channel data
    setDevices(prevDevices => {
      const updatedDevices = [...prevDevices];

      // Process each channel
      for (const channelData of channelsData) {
        const channelIndex = channelData.channel;

        // Validate channel data
        if (
          !Array.isArray(channelData.progress) ||
          !Array.isArray(channelData.elapsed_time) ||
          channelData.progress.length === 0 ||
          channelData.elapsed_time.length === 0
        ) {
          console.warn(`Invalid channel data for channel ${channelIndex}:`, channelData);
          continue;
        }

        // Calculate base index for this channel (3 devices per channel)
        const baseIndex = (channelIndex - 1) * 3;

        // Process each device in the channel
        for (let i = 0; i < Math.min(3, channelData.progress.length); i++) {
          const deviceArrayIndex = baseIndex + i;

          // Skip if device index is out of bounds
          if (deviceArrayIndex >= updatedDevices.length) {
            console.warn(`Device index ${deviceArrayIndex} out of bounds`);
            continue;
          }

          // Check if PCB is present for this device
          const hasPCB = pcbStatusArray[deviceArrayIndex] === true;

          // Get the previous device state
          const prevDevice = updatedDevices[deviceArrayIndex];

          // Skip devices without PCBs - they should remain unchanged with static dummy values
          if (!hasPCB) {
            continue;
          }

          // Get charging and discharging values - preserve previous values if not present
          // This ensures values are not reset when switching between states
          const chargingVoltage =
            channelData.charging?.V?.[i] !== undefined
              ? channelData.charging.V[i]
              : prevDevice.chargingVoltage || 0;

          const chargingCurrent =
            channelData.charging?.C?.[i] !== undefined
              ? channelData.charging.C[i]
              : prevDevice.chargingCurrent || 0;

          const dischargingVoltage =
            channelData.discharging?.V?.[i] !== undefined
              ? channelData.discharging.V[i]
              : prevDevice.dischargingVoltage || 0;

          const dischargingCurrent =
            channelData.discharging?.C?.[i] !== undefined
              ? channelData.discharging.C[i]
              : prevDevice.dischargingCurrent || 0;

          console.log(`Preserved values for device ${deviceArrayIndex + 1}:`, {
            chargingVoltage,
            chargingCurrent,
            dischargingVoltage,
            dischargingCurrent,
            prevChargingVoltage: prevDevice.chargingVoltage,
            prevChargingCurrent: prevDevice.chargingCurrent,
            prevDischargingVoltage: prevDevice.dischargingVoltage,
            prevDischargingCurrent: prevDevice.dischargingCurrent,
          });

          // For devices with PCBs, always update to IN_PROGRESS when channel data is received
          // This ensures the progress bar is displayed
          updatedDevices[deviceArrayIndex] = {
            ...prevDevice,
            testStatus: TEST_STATUS.IN_PROGRESS,
            // Preserve charging/discharging values
            chargingVoltage,
            chargingCurrent,
            dischargingVoltage,
            dischargingCurrent,
          };

          // Get the updated device state after potential changes
          const updatedDevice = updatedDevices[deviceArrayIndex];

          // Extract data for this device
          const progress = channelData.progress[i];
          const elapsedTime = channelData.elapsed_time[i];
          const status = channelData.status?.[i];

          // Log the extracted data for debugging
          console.log(`Channel data for device ${deviceArrayIndex + 1}:`, {
            progress,
            elapsedTime,
            status,
            dischargingV: channelData.discharging?.V?.[i],
            dischargingC: channelData.discharging?.C?.[i],
          });

          // Check if channel data indicates charging/discharging
          let hasChargingData = false;
          let hasDischargingData = false;

          // Check for charging data (ONLY if present in the current channel data)
          // Do NOT consider preserved values for state determination
          if (
            (channelData.charging?.V?.[i] !== undefined && channelData.charging.V[i] > 0) ||
            (channelData.charging?.C?.[i] !== undefined && channelData.charging.C[i] > 0)
          ) {
            hasChargingData = true;
          }

          // Check for discharging data (ONLY if present in the current channel data)
          // Do NOT consider preserved values for state determination
          if (
            (channelData.discharging?.V?.[i] !== undefined && channelData.discharging.V[i] > 0) ||
            (channelData.discharging?.C?.[i] !== undefined && channelData.discharging.C[i] > 0)
          ) {
            hasDischargingData = true;
          }

          console.log(`State determination for device ${deviceArrayIndex + 1}:`, {
            hasChargingDataInChannel: hasChargingData,
            hasDischargingDataInChannel: hasDischargingData,
            hasChargingProperty: channelData.charging !== undefined,
            hasDischargingProperty: channelData.discharging !== undefined,
            channelDataKeys: Object.keys(channelData),
            channelDataStructure: JSON.stringify(channelData).substring(0, 100) + '...',
          });

          // Log if both charging and discharging data are present
          if (hasChargingData && hasDischargingData) {
            console.log(
              `Device ${deviceArrayIndex + 1} has both charging and discharging data in channel`
            );
          }

          // Determine which states are active based on current state and channel data
          let isChargingActive = false;
          let isDischargingActive = false;
          let isChargingAndDischargingActive = false;

          // Set active states based on current state
          if (currentState === 1) {
            isChargingActive = true;
          } else if (currentState === 2) {
            isDischargingActive = true;
          } else if (currentState === 3) {
            isChargingAndDischargingActive = true;
          }

          // Only use current state to determine active states
          // Do NOT override the current state based on preserved data
          // This prevents incorrect state determination

          // If current state is not explicitly set, then use channel data properties
          // Check for the presence of properties in the channel data, not just values
          if (currentState === 0) {
            if (channelData.charging && !channelData.discharging) {
              isChargingActive = true;
            } else if (!channelData.charging && channelData.discharging) {
              isDischargingActive = true;
            } else if (channelData.charging && channelData.discharging) {
              isChargingAndDischargingActive = true;
            }
          }

          console.log(`Final state determination for device ${deviceArrayIndex + 1}:`, {
            currentState,
            isChargingActive,
            isDischargingActive,
            isChargingAndDischargingActive,
          });

          // Create a new device object with updated values
          const newDeviceData = {
            ...updatedDevice,
            // Always set to IN_PROGRESS when channel data is received for devices with PCBs
            testStatus: TEST_STATUS.IN_PROGRESS,
            // Set active states based on current state and channel data
            isCharging: isChargingActive,
            isDischarging: isDischargingActive,
            isChargingAndDischarging: isChargingAndDischargingActive,
            // Track which states have occurred based on current state
            hasState3Occurred: currentState >= 3 ? true : updatedDevice.hasState3Occurred,
            // Preserve the battery level from the initial connection
            batteryLevel: batteryLevels[deviceArrayIndex] || updatedDevice.batteryLevel,
          };

          // Update progress if available
          if (typeof progress === 'number') {
            newDeviceData.progress = progress;
          }

          // Update elapsed time if available
          if (typeof elapsedTime === 'number') {
            newDeviceData.timeRemaining = elapsedTime;
          }

          // Update charging voltage if available
          if (channelData.charging?.V?.[i] !== undefined) {
            newDeviceData.chargingVoltage = channelData.charging.V[i];
          }

          // Update charging current if available
          if (channelData.charging?.C?.[i] !== undefined) {
            newDeviceData.chargingCurrent = channelData.charging.C[i];
          }

          // Update discharging voltage if available
          if (channelData.discharging?.V?.[i] !== undefined) {
            newDeviceData.dischargingVoltage = channelData.discharging.V[i];
          }

          // Update discharging current if available
          if (channelData.discharging?.C?.[i] !== undefined) {
            newDeviceData.dischargingCurrent = channelData.discharging.C[i];
          }

          // Log the updated device data for debugging
          console.log(`Updated device ${deviceArrayIndex + 1} data:`, {
            progress: newDeviceData.progress,
            timeRemaining: newDeviceData.timeRemaining,
            chargingVoltage: newDeviceData.chargingVoltage,
            chargingCurrent: newDeviceData.chargingCurrent,
            dischargingVoltage: newDeviceData.dischargingVoltage,
            dischargingCurrent: newDeviceData.dischargingCurrent,
            isCharging: newDeviceData.isCharging,
            isDischarging: newDeviceData.isDischarging,
            isChargingAndDischarging: newDeviceData.isChargingAndDischarging,
          });

          // Update the device in the array
          updatedDevices[deviceArrayIndex] = newDeviceData;

          // Always update status values based on the current state and channel data
          if (status !== undefined) {
            // Log the status update for debugging
            console.log(`Updating status for device ${deviceArrayIndex + 1}:`, {
              currentState,
              status,
              hasPCB,
            });

            // Convert boolean status to 'ok' or 'error' string values
            const statusValue = status ? STATUS.OK : STATUS.ERROR;

            if (currentState === 1) {
              // Update charging status for all devices - use STATUS.OK/ERROR instead of boolean
              updatedDevices[deviceArrayIndex].chargingStatus = statusValue;
              updatedDevices[deviceArrayIndex].chargingStatusMessage = hasPCB
                ? status
                  ? 'Success'
                  : 'Failed'
                : 'No PCB';
            } else if (currentState === 2) {
              // Update discharging status for all devices - use STATUS.OK/ERROR instead of boolean
              updatedDevices[deviceArrayIndex].dischargingStatus = statusValue;
              updatedDevices[deviceArrayIndex].dischargingStatusMessage = hasPCB
                ? status
                  ? 'Success'
                  : 'Failed'
                : 'No PCB';
            } else if (currentState === 3) {
              // Update charging and discharging status for all devices - use STATUS.OK/ERROR instead of boolean
              updatedDevices[deviceArrayIndex].chargingAndDischargingStatus = statusValue;
              updatedDevices[deviceArrayIndex].chargingAndDischargingStatusMessage = hasPCB
                ? status
                  ? 'Success'
                  : 'Failed'
                : 'No PCB';

              // Mark that state 3 has occurred
              updatedDevices[deviceArrayIndex].hasState3Occurred = true;
            }
          }

          // Update test results based on channel data status and current state
          // This tracks the success/failure of individual operations
          // but doesn't change the overall test status yet
          if (status !== undefined) {
            // Determine which operation this status applies to based on current state
            if (currentState === 1) {
              // Charging state
              setTestResults(prev => ({
                ...prev,
                [deviceArrayIndex + 1]: {
                  ...prev[deviceArrayIndex + 1],
                  chargingSuccess: status,
                },
              }));
            } else if (currentState === 2) {
              // Discharging state
              setTestResults(prev => ({
                ...prev,
                [deviceArrayIndex + 1]: {
                  ...prev[deviceArrayIndex + 1],
                  dischargingSuccess: status,
                },
              }));
            } else if (currentState === 3) {
              // Combined charging and discharging state
              setTestResults(prev => ({
                ...prev,
                [deviceArrayIndex + 1]: {
                  ...prev[deviceArrayIndex + 1],
                  chargingAndDischargingSuccess: status,
                },
              }));
            }

            // Log the status update
            console.log(`Device ${deviceArrayIndex + 1} status update:`, {
              currentState,
              status,
              deviceStatus: updatedDevices[deviceArrayIndex].testStatus,
            });
          }
        }
      }

      return updatedDevices;
    });
  };

  return {
    devices,
    testResults,
    testStats,
    serialNumber,
  };
}
